"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/members/page",{

/***/ "(app-pages-browser)/./src/app/members/page.tsx":
/*!**********************************!*\
  !*** ./src/app/members/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MembersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers_index__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/index */ \"(app-pages-browser)/./src/components/providers/index.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_export__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/export */ \"(app-pages-browser)/./src/lib/export.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/members/add-member-modal */ \"(app-pages-browser)/./src/components/members/add-member-modal.tsx\");\n/* harmony import */ var _components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/members/edit-member-modal */ \"(app-pages-browser)/./src/components/members/edit-member-modal.tsx\");\n/* harmony import */ var _components_members_members_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/members/members-table */ \"(app-pages-browser)/./src/components/members/members-table.tsx\");\n/* harmony import */ var _components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/categories/categories-modal */ \"(app-pages-browser)/./src/components/categories/categories-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tags.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Remove database imports - using localStorage instead\n\n\n\n\n\n\n\nfunction MembersPage() {\n    _s();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMembers, setFilteredMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedMembers, setSelectedMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCategoriesModal, setShowCategoriesModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMember, setEditingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMembers();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterMembers();\n    }, [\n        members,\n        searchQuery,\n        statusFilter\n    ]);\n    const fetchMembers = ()=>{\n        try {\n            setLoading(true);\n            // Fetch users from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            // Fetch subscriptions from localStorage\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            // Map users with their subscriptions\n            const membersWithSubscriptions = users.map((user)=>({\n                    ...user,\n                    subscriptions: subscriptions.filter((sub)=>sub.user_id === user.id).map((sub)=>({\n                            id: sub.id,\n                            sport: sub.sport,\n                            plan_type: sub.plan_type,\n                            start_date: sub.start_date,\n                            end_date: sub.end_date,\n                            price_dzd: sub.price_dzd,\n                            status: sub.status\n                        }))\n                }));\n            setMembers(membersWithSubscriptions);\n        } catch (error) {\n            console.error(\"Error loading members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load members from localStorage\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMembers = ()=>{\n        // Add safety checks for member data\n        const validMembers = members.filter((member)=>member && typeof member === \"object\" && member.full_name && member.phone);\n        let filtered = validMembers.filter((member)=>{\n            var _member_full_name, _member_phone;\n            const nameMatch = ((_member_full_name = member.full_name) === null || _member_full_name === void 0 ? void 0 : _member_full_name.toLowerCase().includes(searchQuery.toLowerCase())) || false;\n            const phoneMatch = ((_member_phone = member.phone) === null || _member_phone === void 0 ? void 0 : _member_phone.includes(searchQuery)) || false;\n            const emailMatch = member.email ? member.email.toLowerCase().includes(searchQuery.toLowerCase()) : false;\n            return nameMatch || phoneMatch || emailMatch;\n        });\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((member)=>{\n                const subscriptions = member.subscriptions || [];\n                if (subscriptions.length === 0) {\n                    return statusFilter === \"expired\";\n                }\n                const activeSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(sub.end_date) === statusFilter);\n                return activeSubscriptions && activeSubscriptions.length > 0;\n            });\n        }\n        setFilteredMembers(filtered);\n    };\n    const getActiveSubscription = (member)=>{\n        if (!member) return null;\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return null;\n        // Get the most recent active subscription\n        const activeSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(sub.end_date) !== \"expired\").sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        return activeSubscriptions[0] || subscriptions[0] || null;\n    };\n    const getMemberStatus = (member)=>{\n        if (!member) return \"expired\";\n        const activeSub = getActiveSubscription(member);\n        if (!activeSub || !activeSub.end_date) return \"expired\";\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(activeSub.end_date);\n    };\n    const deleteMember = (memberId)=>{\n        if (!confirm(\"Are you sure you want to delete this member?\")) return;\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>user.id !== memberId);\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>sub.user_id !== memberId);\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: t(\"member_deleted\"),\n                description: \"Member has been successfully deleted\"\n            });\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting member:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete member\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Export functions\n    const handleExportCSV = ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        const csvData = (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.convertToCSV)(exportData);\n        (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadCSV)(csvData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to CSV successfully\"\n        });\n    };\n    const handleExportExcel = async ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        await (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadExcel)(exportData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to Excel successfully\"\n        });\n    };\n    // Bulk operations\n    const toggleSelectAll = ()=>{\n        if (selectedMembers.size === filteredMembers.length) {\n            setSelectedMembers(new Set());\n        } else {\n            setSelectedMembers(new Set(filteredMembers.map((m)=>m.id)));\n        }\n    };\n    const handleBulkDelete = ()=>{\n        if (selectedMembers.size === 0) return;\n        if (!confirm(\"Are you sure you want to delete \".concat(selectedMembers.size, \" members?\"))) return;\n        setBulkLoading(true);\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>!selectedMembers.has(user.id));\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>!selectedMembers.has(sub.user_id));\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: \"Bulk Delete Complete\",\n                description: \"\".concat(selectedMembers.size, \" members deleted successfully\")\n            });\n            setSelectedMembers(new Set());\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete selected members\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    const handleEditMember = (member)=>{\n        setEditingMember(member);\n        setShowEditModal(true);\n    };\n    const handleViewDetails = (member)=>{\n        // For now, just show an alert with member details\n        const subscriptionCount = member.subscriptions ? member.subscriptions.length : 0;\n        alert(\"Member Details:\\n\\nName: \".concat(member.full_name, \"\\nPhone: \").concat(member.phone, \"\\nEmail: \").concat(member.email || \"N/A\", \"\\nAge: \").concat(member.age, \"\\nGender: \").concat(member.gender, \"\\nSubscriptions: \").concat(subscriptionCount));\n    };\n    const handlePrintReport = (member)=>{\n        // Create a simple print report\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            const subscriptions = member.subscriptions || [];\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Member Report - \".concat(member.full_name, '</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; }\\n              .header { text-align: center; margin-bottom: 30px; }\\n              .info { margin-bottom: 20px; }\\n              .label { font-weight: bold; }\\n              table { width: 100%; border-collapse: collapse; margin-top: 20px; }\\n              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\\n              th { background-color: #f2f2f2; }\\n            </style>\\n          </head>\\n          <body>\\n            <div class=\"header\">\\n              <h1>\\xc9LITE CLUB</h1>\\n              <h2>Member Report</h2>\\n            </div>\\n            <div class=\"info\">\\n              <p><span class=\"label\">Name:</span> ').concat(member.full_name, '</p>\\n              <p><span class=\"label\">Phone:</span> ').concat(member.phone, '</p>\\n              <p><span class=\"label\">Email:</span> ').concat(member.email || \"N/A\", '</p>\\n              <p><span class=\"label\">Age:</span> ').concat(member.age, '</p>\\n              <p><span class=\"label\">Gender:</span> ').concat(member.gender, '</p>\\n              <p><span class=\"label\">Status:</span> ').concat(member.situation, '</p>\\n              <p><span class=\"label\">Member Since:</span> ').concat(new Date(member.created_at).toLocaleDateString(), \"</p>\\n            </div>\\n            <h3>Subscriptions</h3>\\n            <table>\\n              <tr>\\n                <th>Sport</th>\\n                <th>Plan</th>\\n                <th>Start Date</th>\\n                <th>End Date</th>\\n                <th>Price</th>\\n                <th>Status</th>\\n              </tr>\\n              \").concat(subscriptions.map((sub)=>\"\\n                <tr>\\n                  <td>\".concat(sub.sport, \"</td>\\n                  <td>\").concat(sub.plan_type, \"</td>\\n                  <td>\").concat(sub.start_date, \"</td>\\n                  <td>\").concat(sub.end_date, \"</td>\\n                  <td>\").concat(sub.price_dzd, \" DZD</td>\\n                  <td>\").concat(sub.status, \"</td>\\n                </tr>\\n              \")).join(\"\"), '\\n            </table>\\n            <div style=\"margin-top: 40px; text-align: center; font-size: 12px;\">\\n              <p>Generated on ').concat(new Date().toLocaleString(), \"</p>\\n              <p>All rights reserved - Powered by iCode DZ Tel: +213 551 93 05 89</p>\\n            </div>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"members\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                lineNumber: 378,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 377,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"members\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-black text-gray-900 dark:text-white tracking-tight\",\n                                    children: t(\"members_management\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-semibold text-gray-600 dark:text-gray-400\",\n                                    children: t(\"manage_gym_members\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                    onClick: handleExportCSV,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_csv\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                    onClick: handleExportExcel,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_excel\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                selectedMembers.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"bulk_operations\"),\n                                                    \" (\",\n                                                    selectedMembers.size,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                onClick: handleBulkDelete,\n                                                className: \"text-red-600 dark:text-red-400\",\n                                                disabled: bulkLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"delete_selected\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCategoriesModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Categories\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            onClick: ()=>setShowAddModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"add_new_member\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Total Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"active\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-600 dark:text-orange-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expiring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expiring\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expired\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: toggleSelectAll,\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            selectedMembers.size === filteredMembers.length && filteredMembers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: selectedMembers.size > 0 ? \"\".concat(selectedMembers.size, \" \").concat(t(\"selected_count\")) : t(\"select_all\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t(\"search_members\"),\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        \"all\",\n                                        \"active\",\n                                        \"expiring\",\n                                        \"expired\"\n                                    ].map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: statusFilter === status ? \"gym\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setStatusFilter(status),\n                                            className: \"capitalize\",\n                                            children: t(status)\n                                        }, status, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_members_table__WEBPACK_IMPORTED_MODULE_11__.MembersTable, {\n                            members: filteredMembers,\n                            loading: loading,\n                            selectedMembers: selectedMembers,\n                            onSelectionChange: setSelectedMembers,\n                            onEdit: handleEditMember,\n                            onDelete: deleteMember,\n                            onViewDetails: handleViewDetails,\n                            onPrintReport: handlePrintReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 9\n                }, this),\n                filteredMembers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: t(\"no_members_found\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                children: searchQuery || statusFilter !== \"all\" ? t(\"try_adjusting_search\") : t(\"get_started_adding\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"gym\",\n                                onClick: ()=>setShowAddModal(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this),\n                                    t(\"add_new_member\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_9__.AddMemberModal, {\n                    open: showAddModal,\n                    onOpenChange: setShowAddModal,\n                    onMemberAdded: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_10__.EditMemberModal, {\n                    open: showEditModal,\n                    onOpenChange: setShowEditModal,\n                    member: editingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_12__.CategoriesModal, {\n                    open: showCategoriesModal,\n                    onOpenChange: setShowCategoriesModal,\n                    onCategoriesUpdated: ()=>{\n                        // Categories updated - could refresh product categories if needed\n                        toast({\n                            title: \"Categories Updated\",\n                            description: \"Product categories have been updated successfully\"\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 387,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n        lineNumber: 386,\n        columnNumber: 5\n    }, this);\n}\n_s(MembersPage, \"Akb93mmmTplPaqNX9zXmUxywnTc=\", false, function() {\n    return [\n        _components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = MembersPage;\nvar _c;\n$RefreshReg$(_c, \"MembersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/members/page.tsx\n"));

/***/ })

});