'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Activity,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Dumbbell,
  Target,
  Zap,
  Heart,
  Flame,
  Trophy,
  Users,
  Timer,
} from 'lucide-react'

interface Sport {
  id: string
  sport: string
  gender: 'male' | 'female' | 'both'
  age_group: 'child' | 'adult' | 'senior' | 'all'
  monthly_price: number
  quarterly_price: number
  yearly_price: number
  pregnancy_allowed: boolean
  created_at: string
  updated_at: string
}

interface SportsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSportsUpdated: () => void
}

const iconMap = {
  Dumbbell,
  Target,
  Zap,
  Heart,
  Flame,
  Trophy,
  Users,
  Timer,
  Activity,
}

export function CategoriesModal({ open, onOpenChange, onSportsUpdated }: SportsModalProps) {
  const [sports, setSports] = useState<Sport[]>([])
  const [loading, setLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    sport: '',
    gender: 'both' as 'male' | 'female' | 'both',
    age_group: 'all' as 'child' | 'adult' | 'senior' | 'all',
    monthly_price: '',
    quarterly_price: '',
    yearly_price: '',
    pregnancy_allowed: true
  })
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    if (open) {
      fetchSports()
    }
  }, [open])

  const fetchSports = () => {
    try {
      setLoading(true)
      const storedSports = localStorage.getItem('gym_sports')
      const sportsData = storedSports ? JSON.parse(storedSports) : []
      setSports(sportsData)
    } catch (error) {
      console.error('Error fetching sports:', error)
      toast({
        title: 'Error',
        description: 'Failed to load sports',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.sport.trim()) {
      toast({
        title: 'Error',
        description: 'Sport name is required',
        variant: 'destructive',
      })
      return
    }

    if (!formData.monthly_price || !formData.quarterly_price || !formData.yearly_price) {
      toast({
        title: 'Error',
        description: 'All prices are required',
        variant: 'destructive',
      })
      return
    }

    try {
      setLoading(true)

      const storedSports = localStorage.getItem('gym_sports')
      const sportsData = storedSports ? JSON.parse(storedSports) : []

      if (editingId) {
        // Update existing sport
        const updatedSports = sportsData.map((sport: Sport) =>
          sport.id === editingId
            ? {
                ...sport,
                sport: formData.sport.trim(),
                gender: formData.gender,
                age_group: formData.age_group,
                monthly_price: parseFloat(formData.monthly_price),
                quarterly_price: parseFloat(formData.quarterly_price),
                yearly_price: parseFloat(formData.yearly_price),
                pregnancy_allowed: formData.pregnancy_allowed,
                updated_at: new Date().toISOString()
              }
            : sport
        )
        localStorage.setItem('gym_sports', JSON.stringify(updatedSports))

        toast({
          title: 'Success',
          description: 'Sport updated successfully',
        })
      } else {
        // Create new sport
        const newSport: Sport = {
          id: Date.now().toString(),
          sport: formData.sport.trim(),
          gender: formData.gender,
          age_group: formData.age_group,
          monthly_price: parseFloat(formData.monthly_price),
          quarterly_price: parseFloat(formData.quarterly_price),
          yearly_price: parseFloat(formData.yearly_price),
          pregnancy_allowed: formData.pregnancy_allowed,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        sportsData.push(newSport)
        localStorage.setItem('gym_sports', JSON.stringify(sportsData))

        toast({
          title: 'Success',
          description: 'Sport created successfully',
        })
      }

      // Reset form
      setFormData({
        sport: '',
        gender: 'both',
        age_group: 'all',
        monthly_price: '',
        quarterly_price: '',
        yearly_price: '',
        pregnancy_allowed: true
      })
      setEditingId(null)
      setShowAddForm(false)

      fetchSports()
      onSportsUpdated()
    } catch (error: any) {
      console.error('Error saving sport:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to save sport',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (sport: Sport) => {
    setFormData({
      sport: sport.sport,
      gender: sport.gender,
      age_group: sport.age_group,
      monthly_price: sport.monthly_price.toString(),
      quarterly_price: sport.quarterly_price.toString(),
      yearly_price: sport.yearly_price.toString(),
      pregnancy_allowed: sport.pregnancy_allowed
    })
    setEditingId(sport.id)
    setShowAddForm(true)
  }

  const handleDelete = (sportId: string) => {
    if (!confirm('Are you sure you want to delete this sport?')) return

    try {
      setLoading(true)
      const storedSports = localStorage.getItem('gym_sports')
      const sportsData = storedSports ? JSON.parse(storedSports) : []

      const updatedSports = sportsData.filter((sport: Sport) => sport.id !== sportId)
      localStorage.setItem('gym_sports', JSON.stringify(updatedSports))

      toast({
        title: 'Success',
        description: 'Sport deleted successfully',
      })

      fetchSports()
      onSportsUpdated()
    } catch (error: any) {
      console.error('Error deleting sport:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete sport',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      sport: '',
      gender: 'both',
      age_group: 'all',
      monthly_price: '',
      quarterly_price: '',
      yearly_price: '',
      pregnancy_allowed: true
    })
    setEditingId(null)
    setShowAddForm(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>{t('manage_sports')}</span>
          </DialogTitle>
          <DialogDescription>
            {t('manage_sports_description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add/Edit Form */}
          {showAddForm && (
            <Card className="glass border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{editingId ? t('edit_sport') : t('add_new_sport')}</span>
                  <Button variant="ghost" size="sm" onClick={resetForm}>
                    <X className="w-4 h-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Sport Name */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('sport')} *
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.sport}
                        onChange={(e) => setFormData(prev => ({ ...prev, sport: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                        placeholder="Enter sport name"
                      />
                    </div>

                    {/* Gender */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('gender')} *
                      </label>
                      <select
                        required
                        value={formData.gender}
                        onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value as 'male' | 'female' | 'both' }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      >
                        <option value="both">Both</option>
                        <option value="male">{t('male')}</option>
                        <option value="female">{t('female')}</option>
                      </select>
                    </div>

                    {/* Age Group */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Age Group *
                      </label>
                      <select
                        required
                        value={formData.age_group}
                        onChange={(e) => setFormData(prev => ({ ...prev, age_group: e.target.value as 'child' | 'adult' | 'senior' | 'all' }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      >
                        <option value="all">All Ages</option>
                        <option value="child">Child (6-12)</option>
                        <option value="adult">Adult (13-59)</option>
                        <option value="senior">Senior (60+)</option>
                      </select>
                    </div>

                    {/* Monthly Price */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Monthly Price (DA) *
                      </label>
                      <input
                        type="number"
                        required
                        min="0"
                        step="0.01"
                        value={formData.monthly_price}
                        onChange={(e) => setFormData(prev => ({ ...prev, monthly_price: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                        placeholder="0.00"
                      />
                    </div>
                    {/* Quarterly Price */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Quarterly Price (DA) *
                      </label>
                      <input
                        type="number"
                        required
                        min="0"
                        step="0.01"
                        value={formData.quarterly_price}
                        onChange={(e) => setFormData(prev => ({ ...prev, quarterly_price: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Yearly Price */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Yearly Price (DA) *
                      </label>
                      <input
                        type="number"
                        required
                        min="0"
                        step="0.01"
                        value={formData.yearly_price}
                        onChange={(e) => setFormData(prev => ({ ...prev, yearly_price: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  {/* Pregnancy Allowed */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="pregnancy_allowed"
                      checked={formData.pregnancy_allowed}
                      onChange={(e) => setFormData(prev => ({ ...prev, pregnancy_allowed: e.target.checked }))}
                      className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                    <label htmlFor="pregnancy_allowed" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Allow during pregnancy
                    </label>
                  </div>

                  {/* Form Actions */}
                  <div className="flex justify-end space-x-3">
                    <Button type="button" variant="outline" onClick={resetForm}>
                      Cancel
                    </Button>
                    <Button type="submit" variant="gym" disabled={loading}>
                      {loading ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      ) : (
                        <Save className="w-4 h-4 mr-2" />
                      )}
                      {editingId ? 'Update' : 'Create'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Sports List */}
          <Card className="glass border-white/20">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{t('sports')} ({sports.length})</CardTitle>
                {!showAddForm && (
                  <Button variant="gym" onClick={() => setShowAddForm(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    {t('add_new_sport')}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
                </div>
              ) : sports.length === 0 ? (
                <div className="text-center py-8">
                  <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No sports found
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Get started by adding your first sport
                  </p>
                  <Button variant="gym" onClick={() => setShowAddForm(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    {t('add_new_sport')}
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('sport')}</TableHead>
                      <TableHead>{t('gender')}</TableHead>
                      <TableHead>Age Group</TableHead>
                      <TableHead>Monthly Price</TableHead>
                      <TableHead>Quarterly Price</TableHead>
                      <TableHead>Yearly Price</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sports.map((sport) => (
                      <TableRow key={sport.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 rounded-lg bg-red-500 flex items-center justify-center">
                              <Dumbbell className="w-4 h-4 text-white" />
                            </div>
                            <span className="font-medium">{sport.sport}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-gray-600 dark:text-gray-400 capitalize">
                          {sport.gender}
                        </TableCell>
                        <TableCell className="text-gray-600 dark:text-gray-400 capitalize">
                          {sport.age_group}
                        </TableCell>
                        <TableCell className="text-gray-600 dark:text-gray-400">
                          {sport.monthly_price.toLocaleString()} DA
                        </TableCell>
                        <TableCell className="text-gray-600 dark:text-gray-400">
                          {sport.quarterly_price.toLocaleString()} DA
                        </TableCell>
                        <TableCell className="text-gray-600 dark:text-gray-400">
                          {sport.yearly_price.toLocaleString()} DA
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(sport)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(sport.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
