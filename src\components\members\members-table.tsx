'use client'

import { useState } from 'react'
import { useLanguage } from '@/components/providers'
import { formatCurrency, formatDate, getDaysUntilExpiry, getSubscriptionStatus } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Phone,
  Mail,
  Calendar,
  User,
  Printer,
  FileText,
} from 'lucide-react'

interface Subscription {
  id: string
  sport: string
  plan_type: 'monthly' | 'quarterly' | 'yearly'
  start_date: string
  end_date: string
  price_dzd: number
  status: 'active' | 'expiring' | 'expired'
}

interface Member {
  id: string
  full_name: string
  gender: 'male' | 'female'
  age: number
  phone: string
  email: string | null
  pregnant: boolean
  situation: string
  remarks: string | null
  created_at: string
  subscriptions: Subscription[]
}

interface MembersTableProps {
  members: Member[]
  loading: boolean
  selectedMembers: Set<string>
  onSelectionChange: (selected: Set<string>) => void
  onEdit: (member: Member) => void
  onDelete: (memberId: string) => void
  onViewDetails: (member: Member) => void
  onPrintReport: (member: Member) => void
}

export function MembersTable({
  members,
  loading,
  selectedMembers,
  onSelectionChange,
  onEdit,
  onDelete,
  onViewDetails,
  onPrintReport
}: MembersTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [memberToDelete, setMemberToDelete] = useState<string | null>(null)
  const { t } = useLanguage()
  const { toast } = useToast()

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(new Set(members.map(m => m.id)))
    } else {
      onSelectionChange(new Set())
    }
  }

  const handleSelectMember = (memberId: string, checked: boolean) => {
    const newSelection = new Set(selectedMembers)
    if (checked) {
      newSelection.add(memberId)
    } else {
      newSelection.delete(memberId)
    }
    onSelectionChange(newSelection)
  }

  const handleDeleteClick = (memberId: string) => {
    setMemberToDelete(memberId)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = () => {
    if (memberToDelete) {
      onDelete(memberToDelete)
      setDeleteDialogOpen(false)
      setMemberToDelete(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500">Active</Badge>
      case 'expiring':
        return <Badge variant="secondary" className="bg-yellow-500">Expiring</Badge>
      case 'expired':
        return <Badge variant="destructive">Expired</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getActiveSubscription = (member: Member) => {
    const subscriptions = member.subscriptions || []
    if (subscriptions.length === 0) return null
    return subscriptions.find(sub => sub.status === 'active') || subscriptions[0]
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    )
  }

  if (members.length === 0) {
    return (
      <div className="text-center py-12">
        <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No members found
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Get started by adding your first member
        </p>
      </div>
    )
  }

  return (
    <>
      <div className="rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 dark:bg-gray-800">
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedMembers.size === members.length && members.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Member</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Subscription</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Expires</TableHead>
              <TableHead>Price</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member) => {
              const activeSubscription = getActiveSubscription(member)
              const daysUntilExpiry = activeSubscription ? getDaysUntilExpiry(activeSubscription.end_date) : null

              return (
                <TableRow key={member.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <TableCell>
                    <Checkbox
                      checked={selectedMembers.has(member.id)}
                      onCheckedChange={(checked) => handleSelectMember(member.id, checked as boolean)}
                    />
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {member.full_name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {member.full_name}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {member.gender === 'male' ? '♂' : '♀'} {member.age} years
                          {member.pregnant && ' • Pregnant'}
                        </p>
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2 text-sm">
                        <Phone className="w-3 h-3 text-gray-400" />
                        <span>{member.phone}</span>
                      </div>
                      {member.email && (
                        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                          <Mail className="w-3 h-3 text-gray-400" />
                          <span>{member.email}</span>
                        </div>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    {activeSubscription ? (
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {activeSubscription.sport}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                          {activeSubscription.plan_type}
                        </p>
                      </div>
                    ) : (
                      <span className="text-gray-400">No subscription</span>
                    )}
                  </TableCell>

                  <TableCell>
                    {activeSubscription ? getStatusBadge(activeSubscription.status) : '-'}
                  </TableCell>

                  <TableCell>
                    {activeSubscription ? (
                      <div>
                        <p className="text-sm font-medium">
                          {formatDate(activeSubscription.end_date)}
                        </p>
                        {daysUntilExpiry !== null && (
                          <p className={`text-xs ${
                            daysUntilExpiry < 0 ? 'text-red-600' :
                            daysUntilExpiry <= 7 ? 'text-yellow-600' :
                            'text-green-600'
                          }`}>
                            {daysUntilExpiry < 0 ? `${Math.abs(daysUntilExpiry)} days ago` :
                             daysUntilExpiry === 0 ? 'Today' :
                             `${daysUntilExpiry} days left`}
                          </p>
                        )}
                      </div>
                    ) : '-'}
                  </TableCell>

                  <TableCell>
                    {activeSubscription ? (
                      <span className="font-medium">
                        {formatCurrency(activeSubscription.price_dzd)}
                      </span>
                    ) : '-'}
                  </TableCell>

                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onViewDetails(member)}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEdit(member)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Member
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onPrintReport(member)}>
                          <Printer className="w-4 h-4 mr-2" />
                          Print Report
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(member.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete Member
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this member? This action cannot be undone.
              All associated subscriptions will also be deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
