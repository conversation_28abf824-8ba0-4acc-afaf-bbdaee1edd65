'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/components/providers'
import { userOperations } from '@/lib/database'
import { useToast } from '@/hooks/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Save, X, User } from 'lucide-react'

interface Subscription {
  id: string
  sport: string
  plan_type: 'monthly' | 'quarterly' | 'yearly'
  start_date: string
  end_date: string
  price_dzd: number
  status: 'active' | 'expiring' | 'expired'
}

interface Member {
  id: string
  full_name: string
  gender: 'male' | 'female'
  age: number
  phone: string
  email: string | null
  pregnant: boolean
  situation: string
  remarks: string | null
  created_at: string
  subscriptions: Subscription[]
}

interface EditMemberModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: Member | null
  onMemberUpdated: () => void
}

export function EditMemberModal({ open, on<PERSON><PERSON><PERSON><PERSON><PERSON>, member, onMemberUpdated }: EditMemberModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    full_name: '',
    gender: 'male' as 'male' | 'female',
    age: '',
    phone: '',
    email: '',
    pregnant: false,
    situation: 'active',
    remarks: ''
  })
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    if (member && open) {
      setFormData({
        full_name: member.full_name,
        gender: member.gender,
        age: member.age.toString(),
        phone: member.phone,
        email: member.email || '',
        pregnant: member.pregnant,
        situation: member.situation,
        remarks: member.remarks || ''
      })
    }
  }, [member, open])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!member) return

    if (!formData.full_name.trim()) {
      toast({
        title: 'Error',
        description: 'Full name is required',
        variant: 'destructive',
      })
      return
    }

    if (!formData.phone.trim()) {
      toast({
        title: 'Error',
        description: 'Phone number is required',
        variant: 'destructive',
      })
      return
    }

    if (!formData.age || parseInt(formData.age) <= 0) {
      toast({
        title: 'Error',
        description: 'Valid age is required',
        variant: 'destructive',
      })
      return
    }

    try {
      setLoading(true)

      const updateData = {
        full_name: formData.full_name.trim(),
        gender: formData.gender,
        age: parseInt(formData.age),
        phone: formData.phone.trim(),
        email: formData.email.trim() || null,
        pregnant: formData.gender === 'female' ? formData.pregnant : false,
        situation: formData.situation,
        remarks: formData.remarks.trim() || null,
      }

      await userOperations.update(member.id, updateData)

      toast({
        title: 'Success',
        description: 'Member updated successfully',
      })

      onMemberUpdated()
      onOpenChange(false)
    } catch (error: any) {
      console.error('Error updating member:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to update member',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  if (!member) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>Edit Member</span>
          </DialogTitle>
          <DialogDescription>
            Update member information and details
          </DialogDescription>
        </DialogHeader>

        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Member Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Full Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.full_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter full name"
                  />
                </div>

                {/* Gender */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Gender *
                  </label>
                  <select
                    required
                    value={formData.gender}
                    onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value as 'male' | 'female' }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                </div>

                {/* Age */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Age *
                  </label>
                  <input
                    type="number"
                    required
                    min="1"
                    max="120"
                    value={formData.age}
                    onChange={(e) => setFormData(prev => ({ ...prev, age: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter age"
                  />
                </div>

                {/* Phone */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    required
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter phone number"
                  />
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter email address"
                  />
                </div>

                {/* Situation */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.situation}
                    onChange={(e) => setFormData(prev => ({ ...prev, situation: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="suspended">Suspended</option>
                  </select>
                </div>
              </div>

              {/* Pregnancy checkbox for females */}
              {formData.gender === 'female' && (
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="pregnant"
                    checked={formData.pregnant}
                    onChange={(e) => setFormData(prev => ({ ...prev, pregnant: e.target.checked }))}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <label htmlFor="pregnant" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Currently pregnant
                  </label>
                </div>
              )}

              {/* Remarks */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Remarks
                </label>
                <textarea
                  value={formData.remarks}
                  onChange={(e) => setFormData(prev => ({ ...prev, remarks: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="Enter any additional remarks"
                />
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit" variant="gym" disabled={loading}>
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  Update Member
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}
