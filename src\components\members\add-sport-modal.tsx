'use client'

import { useState } from 'react'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Plus,
  Save,
  Dumbbell,
} from 'lucide-react'

interface AddSportModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSportAdded: () => void
}

export function AddSportModal({ open, onOpenChange, onSportAdded }: AddSportModalProps) {
  const [formData, setFormData] = useState({
    sport: '',
    gender: 'both' as 'male' | 'female' | 'both',
    age_group: 'all' as 'child' | 'adult' | 'senior' | 'all',
    monthly_price: '',
    quarterly_price: '',
    yearly_price: '',
    pregnancy_allowed: true,
  })
  const [loading, setLoading] = useState(false)
  const { t } = useLanguage()
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate form data
      if (!formData.sport.trim()) {
        throw new Error('Sport name is required')
      }
      if (!formData.monthly_price || parseFloat(formData.monthly_price) <= 0) {
        throw new Error('Monthly price must be greater than 0')
      }
      if (!formData.quarterly_price || parseFloat(formData.quarterly_price) <= 0) {
        throw new Error('Quarterly price must be greater than 0')
      }
      if (!formData.yearly_price || parseFloat(formData.yearly_price) <= 0) {
        throw new Error('Yearly price must be greater than 0')
      }

      // For now, let's simulate adding to local storage until Supabase is configured
      const newSport = {
        id: Date.now().toString(),
        sport: formData.sport.trim(),
        gender: formData.gender,
        age_group: formData.age_group,
        monthly_price: parseFloat(formData.monthly_price),
        quarterly_price: parseFloat(formData.quarterly_price),
        yearly_price: parseFloat(formData.yearly_price),
        pregnancy_allowed: formData.pregnancy_allowed,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      // Store in localStorage
      const existingSports = JSON.parse(localStorage.getItem('gym_sports') || '[]')
      existingSports.push(newSport)
      localStorage.setItem('gym_sports', JSON.stringify(existingSports))

      toast({
        title: 'Sport Added Successfully',
        description: `${formData.sport} has been added to the system`,
      })

      // Reset form
      setFormData({
        sport: '',
        gender: 'both',
        age_group: 'all',
        monthly_price: '',
        quarterly_price: '',
        yearly_price: '',
        pregnancy_allowed: true,
      })

      onSportAdded()
      onOpenChange(false)
    } catch (error: any) {
      console.error('Error adding sport:', error)
      toast({
        title: 'Error Adding Sport',
        description: error.message || 'Failed to add sport. Please check your input and try again.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Auto-calculate quarterly and yearly prices based on monthly
    if (field === 'monthly_price' && value) {
      const monthly = parseFloat(value)
      if (!isNaN(monthly)) {
        setFormData(prev => ({
          ...prev,
          quarterly_price: (monthly * 2.8).toString(), // 10% discount
          yearly_price: (monthly * 10).toString(), // 17% discount
        }))
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Dumbbell className="w-5 h-5" />
            <span>{t('add_new_sport')}</span>
          </DialogTitle>
          <DialogDescription>
            Add a new sport with pricing information
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>Sport Information</CardTitle>
              <CardDescription>
                Basic sport details and target audience
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Sport Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Sport Name *
                </label>
                <input
                  type="text"
                  required
                  value={formData.sport}
                  onChange={(e) => handleInputChange('sport', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="e.g., Advanced Boxing, Senior Yoga"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Gender */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('gender')} Target
                  </label>
                  <select
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="both">Both</option>
                    <option value="male">{t('male')} Only</option>
                    <option value="female">{t('female')} Only</option>
                  </select>
                </div>

                {/* Age Group */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Age Group
                  </label>
                  <select
                    value={formData.age_group}
                    onChange={(e) => handleInputChange('age_group', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="all">All Ages</option>
                    <option value="child">Children (Under 18)</option>
                    <option value="adult">Adults (18-59)</option>
                    <option value="senior">Seniors (60+)</option>
                  </select>
                </div>
              </div>

              {/* Pregnancy Safe */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="pregnancy_allowed"
                  checked={formData.pregnancy_allowed}
                  onChange={(e) => handleInputChange('pregnancy_allowed', e.target.checked)}
                  className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                />
                <label htmlFor="pregnancy_allowed" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Safe during pregnancy
                </label>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>Pricing</CardTitle>
              <CardDescription>
                Set prices for different subscription plans (in DZD)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Monthly Price */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('monthly')} Price (DZD) *
                  </label>
                  <input
                    type="number"
                    required
                    min="0"
                    step="100"
                    value={formData.monthly_price}
                    onChange={(e) => handleInputChange('monthly_price', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="5000"
                  />
                </div>

                {/* Quarterly Price */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('quarterly')} Price (DZD) *
                  </label>
                  <input
                    type="number"
                    required
                    min="0"
                    step="100"
                    value={formData.quarterly_price}
                    onChange={(e) => handleInputChange('quarterly_price', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="14000"
                  />
                </div>

                {/* Yearly Price */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('yearly')} Price (DZD) *
                  </label>
                  <input
                    type="number"
                    required
                    min="0"
                    step="100"
                    value={formData.yearly_price}
                    onChange={(e) => handleInputChange('yearly_price', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="50000"
                  />
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>Tip:</strong> Quarterly and yearly prices are automatically calculated with discounts when you enter the monthly price.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              {t('cancel')}
            </Button>
            <Button
              type="submit"
              variant="gym"
              disabled={loading}
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {t('save')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
