'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/components/providers'
import { formatCurrency, getAgeGroup, calculateEndDate } from '@/lib/utils'
// Remove database imports - using localStorage instead
import { useToast } from '@/hooks/use-toast'
import { AddSportModal } from './add-sport-modal'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  UserPlus,
  Save,
  AlertTriangle,
  Info,
  Plus,
} from 'lucide-react'

interface SportsPricing {
  id: string
  sport: string
  gender: 'male' | 'female' | 'both'
  age_group: 'child' | 'adult' | 'senior' | 'all'
  monthly_price: number
  quarterly_price: number
  yearly_price: number
  pregnancy_allowed: boolean
}

interface AddMemberModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onMemberAdded: () => void
}

export function AddMemberModal({ open, onOpenChange, onMemberAdded }: AddMemberModalProps) {
  const [formData, setFormData] = useState({
    full_name: '',
    gender: 'male' as 'male' | 'female',
    age: '',
    phone: '',
    email: '',
    pregnant: false,
    situation: 'active',
    remarks: '',
    sport: '',
    plan_type: 'monthly' as 'monthly' | 'quarterly' | 'yearly',
  })
  const [sportsPricing, setSportsPricing] = useState<SportsPricing[]>([])
  const [availableSports, setAvailableSports] = useState<SportsPricing[]>([])
  const [selectedSportPricing, setSelectedSportPricing] = useState<SportsPricing | null>(null)
  const [loading, setLoading] = useState(false)
  const [loadingPricing, setLoadingPricing] = useState(true)
  const [showAddSport, setShowAddSport] = useState(false)
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    if (open) {
      fetchSportsPricing()
    }
  }, [open])

  useEffect(() => {
    filterAvailableSports()
  }, [sportsPricing, formData.gender, formData.age, formData.pregnant])

  useEffect(() => {
    updateSelectedSportPricing()
  }, [formData.sport, availableSports])

  const fetchSportsPricing = () => {
    try {
      const storedSports = localStorage.getItem('gym_sports')
      const data = storedSports ? JSON.parse(storedSports) : []
      setSportsPricing(data)
    } catch (error) {
      console.error('Error loading sports:', error)
      toast({
        title: 'Error',
        description: 'Failed to load sports pricing',
        variant: 'destructive',
      })
    } finally {
      setLoadingPricing(false)
    }
  }

  const filterAvailableSports = () => {
    if (!formData.age) {
      setAvailableSports([])
      return
    }

    const ageGroup = getAgeGroup(parseInt(formData.age))

    const filtered = sportsPricing.filter(pricing => {
      const genderMatch = pricing.gender === 'both' || pricing.gender === formData.gender
      const ageMatch = pricing.age_group === 'all' || pricing.age_group === ageGroup
      const pregnancyMatch = !formData.pregnant || pricing.pregnancy_allowed

      return genderMatch && ageMatch && pregnancyMatch
    })

    setAvailableSports(filtered)
  }

  const updateSelectedSportPricing = () => {
    if (!formData.sport) {
      setSelectedSportPricing(null)
      return
    }

    const pricing = availableSports.find(p => p.sport === formData.sport)
    setSelectedSportPricing(pricing || null)
  }

  const getPrice = () => {
    if (!selectedSportPricing) return 0

    switch (formData.plan_type) {
      case 'monthly':
        return selectedSportPricing.monthly_price
      case 'quarterly':
        return selectedSportPricing.quarterly_price
      case 'yearly':
        return selectedSportPricing.yearly_price
      default:
        return 0
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedSportPricing) {
      toast({
        title: 'Error',
        description: 'Please select a sport',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)

    try {
      // Validate form data
      if (!formData.full_name.trim()) {
        throw new Error('Full name is required')
      }
      if (!formData.phone.trim()) {
        throw new Error('Phone number is required')
      }
      if (!formData.age || parseInt(formData.age) <= 0) {
        throw new Error('Valid age is required')
      }

      // Create user in localStorage
      const userId = Date.now().toString()
      const userData = {
        id: userId,
        full_name: formData.full_name.trim(),
        gender: formData.gender,
        age: parseInt(formData.age),
        phone: formData.phone.trim(),
        email: formData.email?.trim() || null,
        pregnant: formData.gender === 'female' ? formData.pregnant : false,
        situation: formData.situation,
        remarks: formData.remarks?.trim() || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      // Save user to localStorage
      const storedUsers = localStorage.getItem('gym_members')
      const users = storedUsers ? JSON.parse(storedUsers) : []
      users.push(userData)
      localStorage.setItem('gym_members', JSON.stringify(users))

      // Create subscription
      const startDate = new Date().toISOString().split('T')[0]
      const endDate = calculateEndDate(startDate, formData.plan_type)

      const subscriptionData = {
        id: (Date.now() + 1).toString(),
        user_id: userId,
        sport: formData.sport,
        plan_type: formData.plan_type,
        start_date: startDate,
        end_date: endDate,
        price_dzd: getPrice(),
        status: 'active' as 'active' | 'expiring' | 'expired',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      // Save subscription to localStorage
      const storedSubscriptions = localStorage.getItem('gym_subscriptions')
      const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : []
      subscriptions.push(subscriptionData)
      localStorage.setItem('gym_subscriptions', JSON.stringify(subscriptions))

      toast({
        title: t('member_added'),
        description: `${formData.full_name} has been successfully registered`,
      })

      // Reset form
      setFormData({
        full_name: '',
        gender: 'male',
        age: '',
        phone: '',
        email: '',
        pregnant: false,
        situation: 'active',
        remarks: '',
        sport: '',
        plan_type: 'monthly',
      })

      onMemberAdded()
      onOpenChange(false)
    } catch (error: any) {
      console.error('Error adding member:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to add member. Please check your input and try again.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (loadingPricing) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <UserPlus className="w-5 h-5" />
            <span>{t('add_new_member')}</span>
          </DialogTitle>
          <DialogDescription>
            Register a new gym member with subscription
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card className="glass border-white/20">
              <CardHeader>
                <CardTitle>{t('personal_information')}</CardTitle>
                <CardDescription>
                  Basic member information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Full Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('full_name')} *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.full_name}
                    onChange={(e) => handleInputChange('full_name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter full name"
                  />
                </div>

                {/* Gender */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('gender')} *
                  </label>
                  <select
                    required
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="male">{t('male')}</option>
                    <option value="female">{t('female')}</option>
                  </select>
                </div>

                {/* Age */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('age')} *
                  </label>
                  <input
                    type="number"
                    required
                    min="1"
                    max="120"
                    value={formData.age}
                    onChange={(e) => handleInputChange('age', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter age"
                  />
                </div>

                {/* Pregnancy checkbox for females */}
                {formData.gender === 'female' && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="pregnant"
                      checked={formData.pregnant}
                      onChange={(e) => handleInputChange('pregnant', e.target.checked)}
                      className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                    <label htmlFor="pregnant" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('pregnant')}
                    </label>
                  </div>
                )}

                {/* Situation */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('situation')}
                  </label>
                  <select
                    value={formData.situation}
                    onChange={(e) => handleInputChange('situation', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="active">{t('active')}</option>
                    <option value="pregnant">Pregnant</option>
                    <option value="sick">Sick</option>
                    <option value="injured">Injured</option>
                    <option value="vacation">Vacation</option>
                  </select>
                </div>

                {/* Remarks */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('remarks')}
                  </label>
                  <textarea
                    value={formData.remarks}
                    onChange={(e) => handleInputChange('remarks', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Additional notes or remarks"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="glass border-white/20">
              <CardHeader>
                <CardTitle>{t('contact_information')}</CardTitle>
                <CardDescription>
                  Contact details and subscription
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Phone */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('phone')} *
                  </label>
                  <input
                    type="tel"
                    required
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="0555123456"
                  />
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('email')}
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>

                {/* Sport Selection */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('sport')} *
                    </label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAddSport(true)}
                      className="text-xs"
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      {t('add_new_sport')}
                    </Button>
                  </div>
                  <select
                    required
                    value={formData.sport}
                    onChange={(e) => handleInputChange('sport', e.target.value)}
                    disabled={!formData.age}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:opacity-50"
                  >
                    <option value="">Select a sport</option>
                    {availableSports.map((pricing) => (
                      <option key={pricing.id} value={pricing.sport}>
                        {pricing.sport}
                      </option>
                    ))}
                  </select>
                  {!formData.age && (
                    <p className="text-sm text-gray-500 mt-1">
                      Please enter age first to see available sports
                    </p>
                  )}
                </div>

                {/* Plan Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('plan_type')} *
                  </label>
                  <select
                    required
                    value={formData.plan_type}
                    onChange={(e) => handleInputChange('plan_type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="monthly">{t('monthly')}</option>
                    <option value="quarterly">{t('quarterly')}</option>
                    <option value="yearly">{t('yearly')}</option>
                  </select>
                </div>

                {/* Price Summary */}
                {selectedSportPricing && (
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-gray-900 dark:text-white">
                        {t('total_amount')}:
                      </span>
                      <span className="text-xl font-bold text-red-600 dark:text-red-400">
                        {formatCurrency(getPrice())}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 mt-2">
                      <Info className="w-4 h-4 text-blue-500" />
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Subscription will start today and end on{' '}
                        {calculateEndDate(new Date().toISOString().split('T')[0], formData.plan_type)}
                      </p>
                    </div>
                  </div>
                )}

                {/* Pregnancy Warning */}
                {formData.pregnant && selectedSportPricing && !selectedSportPricing.pregnancy_allowed && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400" />
                      <p className="text-sm text-red-600 dark:text-red-400 font-medium">
                        Warning: This sport is not recommended during pregnancy
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              {t('cancel')}
            </Button>
            <Button
              type="submit"
              variant="gym"
              disabled={loading || !selectedSportPricing}
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {t('save')}
            </Button>
          </div>
        </form>

        {/* Add Sport Modal */}
        <AddSportModal
          open={showAddSport}
          onOpenChange={setShowAddSport}
          onSportAdded={fetchSportsPricing}
        />
      </DialogContent>
    </Dialog>
  )
}
