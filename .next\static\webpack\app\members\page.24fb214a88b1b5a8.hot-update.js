"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/members/page",{

/***/ "(app-pages-browser)/./src/app/members/page.tsx":
/*!**********************************!*\
  !*** ./src/app/members/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MembersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers_index__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/index */ \"(app-pages-browser)/./src/components/providers/index.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_export__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/export */ \"(app-pages-browser)/./src/lib/export.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/members/add-member-modal */ \"(app-pages-browser)/./src/components/members/add-member-modal.tsx\");\n/* harmony import */ var _components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/members/edit-member-modal */ \"(app-pages-browser)/./src/components/members/edit-member-modal.tsx\");\n/* harmony import */ var _components_members_members_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/members/members-table */ \"(app-pages-browser)/./src/components/members/members-table.tsx\");\n/* harmony import */ var _components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/categories/categories-modal */ \"(app-pages-browser)/./src/components/categories/categories-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tags.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Remove database imports - using localStorage instead\n\n\n\n\n\n\n\nfunction MembersPage() {\n    _s();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMembers, setFilteredMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedMembers, setSelectedMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCategoriesModal, setShowCategoriesModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMember, setEditingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMembers();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterMembers();\n    }, [\n        members,\n        searchQuery,\n        statusFilter\n    ]);\n    const fetchMembers = ()=>{\n        try {\n            setLoading(true);\n            // Fetch users from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            // Fetch subscriptions from localStorage\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            // Map users with their subscriptions\n            const membersWithSubscriptions = users.map((user)=>({\n                    ...user,\n                    subscriptions: subscriptions.filter((sub)=>sub.user_id === user.id).map((sub)=>({\n                            id: sub.id,\n                            sport: sub.sport,\n                            plan_type: sub.plan_type,\n                            start_date: sub.start_date,\n                            end_date: sub.end_date,\n                            price_dzd: sub.price_dzd,\n                            status: sub.status\n                        }))\n                }));\n            setMembers(membersWithSubscriptions);\n        } catch (error) {\n            console.error(\"Error loading members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load members from localStorage\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMembers = ()=>{\n        // Add safety checks for member data\n        const validMembers = members.filter((member)=>member && typeof member === \"object\" && member.full_name && member.phone);\n        let filtered = validMembers.filter((member)=>{\n            var _member_full_name, _member_phone;\n            const nameMatch = ((_member_full_name = member.full_name) === null || _member_full_name === void 0 ? void 0 : _member_full_name.toLowerCase().includes(searchQuery.toLowerCase())) || false;\n            const phoneMatch = ((_member_phone = member.phone) === null || _member_phone === void 0 ? void 0 : _member_phone.includes(searchQuery)) || false;\n            const emailMatch = member.email ? member.email.toLowerCase().includes(searchQuery.toLowerCase()) : false;\n            return nameMatch || phoneMatch || emailMatch;\n        });\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((member)=>{\n                const subscriptions = member.subscriptions || [];\n                if (subscriptions.length === 0) {\n                    return statusFilter === \"expired\";\n                }\n                const activeSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(sub.end_date) === statusFilter);\n                return activeSubscriptions && activeSubscriptions.length > 0;\n            });\n        }\n        setFilteredMembers(filtered);\n    };\n    const getActiveSubscription = (member)=>{\n        if (!member) return null;\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return null;\n        // Get the most recent active subscription\n        const activeSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(sub.end_date) !== \"expired\").sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        return activeSubscriptions[0] || subscriptions[0] || null;\n    };\n    const getMemberStatus = (member)=>{\n        if (!member) return \"expired\";\n        const activeSub = getActiveSubscription(member);\n        if (!activeSub || !activeSub.end_date) return \"expired\";\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(activeSub.end_date);\n    };\n    const deleteMember = (memberId)=>{\n        if (!confirm(\"Are you sure you want to delete this member?\")) return;\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>user.id !== memberId);\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>sub.user_id !== memberId);\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: t(\"member_deleted\"),\n                description: \"Member has been successfully deleted\"\n            });\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting member:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete member\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Export functions\n    const handleExportCSV = ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        const csvData = (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.convertToCSV)(exportData);\n        (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadCSV)(csvData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to CSV successfully\"\n        });\n    };\n    const handleExportExcel = async ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        await (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadExcel)(exportData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to Excel successfully\"\n        });\n    };\n    // Bulk operations\n    const toggleSelectAll = ()=>{\n        if (selectedMembers.size === filteredMembers.length) {\n            setSelectedMembers(new Set());\n        } else {\n            setSelectedMembers(new Set(filteredMembers.map((m)=>m.id)));\n        }\n    };\n    const handleBulkDelete = ()=>{\n        if (selectedMembers.size === 0) return;\n        if (!confirm(\"Are you sure you want to delete \".concat(selectedMembers.size, \" members?\"))) return;\n        setBulkLoading(true);\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>!selectedMembers.has(user.id));\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>!selectedMembers.has(sub.user_id));\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: \"Bulk Delete Complete\",\n                description: \"\".concat(selectedMembers.size, \" members deleted successfully\")\n            });\n            setSelectedMembers(new Set());\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete selected members\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    const handleEditMember = (member)=>{\n        setEditingMember(member);\n        setShowEditModal(true);\n    };\n    const handleViewDetails = (member)=>{\n        // For now, just show an alert with member details\n        const subscriptionCount = member.subscriptions ? member.subscriptions.length : 0;\n        alert(\"Member Details:\\n\\nName: \".concat(member.full_name, \"\\nPhone: \").concat(member.phone, \"\\nEmail: \").concat(member.email || \"N/A\", \"\\nAge: \").concat(member.age, \"\\nGender: \").concat(member.gender, \"\\nSubscriptions: \").concat(subscriptionCount));\n    };\n    const handlePrintReport = (member)=>{\n        // Create a simple print report\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            const subscriptions = member.subscriptions || [];\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Member Report - \".concat(member.full_name, '</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; }\\n              .header { text-align: center; margin-bottom: 30px; }\\n              .info { margin-bottom: 20px; }\\n              .label { font-weight: bold; }\\n              table { width: 100%; border-collapse: collapse; margin-top: 20px; }\\n              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\\n              th { background-color: #f2f2f2; }\\n            </style>\\n          </head>\\n          <body>\\n            <div class=\"header\">\\n              <h1>\\xc9LITE CLUB</h1>\\n              <h2>Member Report</h2>\\n            </div>\\n            <div class=\"info\">\\n              <p><span class=\"label\">Name:</span> ').concat(member.full_name, '</p>\\n              <p><span class=\"label\">Phone:</span> ').concat(member.phone, '</p>\\n              <p><span class=\"label\">Email:</span> ').concat(member.email || \"N/A\", '</p>\\n              <p><span class=\"label\">Age:</span> ').concat(member.age, '</p>\\n              <p><span class=\"label\">Gender:</span> ').concat(member.gender, '</p>\\n              <p><span class=\"label\">Status:</span> ').concat(member.situation, '</p>\\n              <p><span class=\"label\">Member Since:</span> ').concat(new Date(member.created_at).toLocaleDateString(), \"</p>\\n            </div>\\n            <h3>Subscriptions</h3>\\n            <table>\\n              <tr>\\n                <th>Sport</th>\\n                <th>Plan</th>\\n                <th>Start Date</th>\\n                <th>End Date</th>\\n                <th>Price</th>\\n                <th>Status</th>\\n              </tr>\\n              \").concat(subscriptions.map((sub)=>\"\\n                <tr>\\n                  <td>\".concat(sub.sport, \"</td>\\n                  <td>\").concat(sub.plan_type, \"</td>\\n                  <td>\").concat(sub.start_date, \"</td>\\n                  <td>\").concat(sub.end_date, \"</td>\\n                  <td>\").concat(sub.price_dzd, \" DZD</td>\\n                  <td>\").concat(sub.status, \"</td>\\n                </tr>\\n              \")).join(\"\"), '\\n            </table>\\n            <div style=\"margin-top: 40px; text-align: center; font-size: 12px;\">\\n              <p>Generated on ').concat(new Date().toLocaleString(), \"</p>\\n              <p>All rights reserved - Powered by iCode DZ Tel: +213 551 93 05 89</p>\\n            </div>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"members\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                lineNumber: 378,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 377,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"members\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-black text-gray-900 dark:text-white tracking-tight\",\n                                    children: t(\"members_management\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-semibold text-gray-600 dark:text-gray-400\",\n                                    children: t(\"manage_gym_members\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                    onClick: handleExportCSV,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_csv\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                    onClick: handleExportExcel,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_excel\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                selectedMembers.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"bulk_operations\"),\n                                                    \" (\",\n                                                    selectedMembers.size,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                onClick: handleBulkDelete,\n                                                className: \"text-red-600 dark:text-red-400\",\n                                                disabled: bulkLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"delete_selected\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCategoriesModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Categories\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            onClick: ()=>setShowAddModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"add_new_member\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Total Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"active\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-600 dark:text-orange-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expiring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expiring\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expired\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: toggleSelectAll,\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            selectedMembers.size === filteredMembers.length && filteredMembers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: selectedMembers.size > 0 ? \"\".concat(selectedMembers.size, \" \").concat(t(\"selected_count\")) : t(\"select_all\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t(\"search_members\"),\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        \"all\",\n                                        \"active\",\n                                        \"expiring\",\n                                        \"expired\"\n                                    ].map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: statusFilter === status ? \"gym\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setStatusFilter(status),\n                                            className: \"capitalize\",\n                                            children: t(status)\n                                        }, status, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_members_table__WEBPACK_IMPORTED_MODULE_11__.MembersTable, {\n                            members: filteredMembers,\n                            loading: loading,\n                            selectedMembers: selectedMembers,\n                            onSelectionChange: setSelectedMembers,\n                            onEdit: handleEditMember,\n                            onDelete: deleteMember,\n                            onViewDetails: handleViewDetails,\n                            onPrintReport: handlePrintReport,\n                            onMemberUpdated: fetchMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 9\n                }, this),\n                filteredMembers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: t(\"no_members_found\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                children: searchQuery || statusFilter !== \"all\" ? t(\"try_adjusting_search\") : t(\"get_started_adding\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"gym\",\n                                onClick: ()=>setShowAddModal(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, this),\n                                    t(\"add_new_member\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_9__.AddMemberModal, {\n                    open: showAddModal,\n                    onOpenChange: setShowAddModal,\n                    onMemberAdded: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 611,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_10__.EditMemberModal, {\n                    open: showEditModal,\n                    onOpenChange: setShowEditModal,\n                    member: editingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 617,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_12__.CategoriesModal, {\n                    open: showCategoriesModal,\n                    onOpenChange: setShowCategoriesModal,\n                    onSportsUpdated: ()=>{\n                        // Sports updated - could refresh sports list if needed\n                        toast({\n                            title: \"Sports Updated\",\n                            description: \"Sports have been updated successfully\"\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 624,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 387,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n        lineNumber: 386,\n        columnNumber: 5\n    }, this);\n}\n_s(MembersPage, \"Akb93mmmTplPaqNX9zXmUxywnTc=\", false, function() {\n    return [\n        _components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = MembersPage;\nvar _c;\n$RefreshReg$(_c, \"MembersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/members/page.tsx\n"));

/***/ })

});