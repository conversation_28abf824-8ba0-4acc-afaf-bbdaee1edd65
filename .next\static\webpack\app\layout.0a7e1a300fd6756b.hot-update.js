"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"5b884fa7bf2d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YmJmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjViODg0ZmE3YmYyZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: function() { return /* binding */ Providers; },\n/* harmony export */   useLanguage: function() { return /* binding */ useLanguage; },\n/* harmony export */   useTheme: function() { return /* binding */ useTheme; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useTheme,useLanguage,Providers auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    theme: \"light\",\n    toggleTheme: ()=>{}\n});\nconst useTheme = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    language: \"en\",\n    setLanguage: ()=>{},\n    t: (key)=>key\n});\nconst useLanguage = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n_s1(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Simple translations\nconst translations = {\n    en: {\n        \"members\": \"Members\",\n        \"dashboard\": \"Dashboard\",\n        \"pos\": \"Point of Sale\",\n        \"inventory\": \"Inventory\",\n        \"sports\": \"Sports\",\n        \"classes\": \"Classes\",\n        \"reports\": \"Reports\",\n        \"settings\": \"Settings\",\n        \"logout\": \"Logout\",\n        \"add_member\": \"Add Member\",\n        \"edit_member\": \"Edit Member\",\n        \"delete_member\": \"Delete Member\",\n        \"save\": \"Save\",\n        \"cancel\": \"Cancel\",\n        \"name\": \"Name\",\n        \"email\": \"Email\",\n        \"phone\": \"Phone\",\n        \"category\": \"Category\"\n    },\n    fr: {\n        \"members\": \"Membres\",\n        \"dashboard\": \"Tableau de bord\",\n        \"pos\": \"Point de vente\",\n        \"inventory\": \"Inventaire\",\n        \"sports\": \"Sports\",\n        \"classes\": \"Cours\",\n        \"reports\": \"Rapports\",\n        \"settings\": \"Param\\xe8tres\",\n        \"logout\": \"D\\xe9connexion\",\n        \"add_member\": \"Ajouter un membre\",\n        \"edit_member\": \"Modifier le membre\",\n        \"delete_member\": \"Supprimer le membre\",\n        \"save\": \"Enregistrer\",\n        \"cancel\": \"Annuler\",\n        \"name\": \"Nom\",\n        \"email\": \"Email\",\n        \"phone\": \"T\\xe9l\\xe9phone\",\n        \"category\": \"Cat\\xe9gorie\"\n    },\n    ar: {\n        \"members\": \"الأعضاء\",\n        \"dashboard\": \"لوحة التحكم\",\n        \"pos\": \"نقطة البيع\",\n        \"inventory\": \"المخزون\",\n        \"sports\": \"الرياضات\",\n        \"classes\": \"الفصول\",\n        \"reports\": \"التقارير\",\n        \"settings\": \"الإعدادات\",\n        \"logout\": \"تسجيل الخروج\",\n        \"add_member\": \"إضافة عضو\",\n        \"edit_member\": \"تعديل العضو\",\n        \"delete_member\": \"حذف العضو\",\n        \"save\": \"حفظ\",\n        \"cancel\": \"إلغاء\",\n        \"name\": \"الاسم\",\n        \"email\": \"البريد الإلكتروني\",\n        \"phone\": \"الهاتف\",\n        \"category\": \"الفئة\",\n        \"manage_sports\": \"إدارة الرياضات\",\n        \"manage_sports_description\": \"إضافة وتعديل وحذف فئات الرياضة لنظام إدارة النادي\",\n        \"edit_sport\": \"تعديل الرياضة\",\n        \"member_updated\": \"تم تحديث العضو\"\n    }\n};\nfunction Providers(param) {\n    let { children } = param;\n    _s2();\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load theme from localStorage\n        const savedTheme = localStorage.getItem(\"gym-theme\");\n        if (savedTheme) {\n            setTheme(savedTheme);\n            document.documentElement.classList.toggle(\"dark\", savedTheme === \"dark\");\n        }\n        // Load language from localStorage\n        const savedLanguage = localStorage.getItem(\"gym-language\");\n        if (savedLanguage) {\n            setLanguage(savedLanguage);\n            document.documentElement.setAttribute(\"lang\", savedLanguage);\n            document.documentElement.setAttribute(\"dir\", savedLanguage === \"ar\" ? \"rtl\" : \"ltr\");\n        }\n    }, []);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        localStorage.setItem(\"gym-theme\", newTheme);\n        document.documentElement.classList.toggle(\"dark\", newTheme === \"dark\");\n    };\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        localStorage.setItem(\"gym-language\", lang);\n        document.documentElement.setAttribute(\"lang\", lang);\n        document.documentElement.setAttribute(\"dir\", lang === \"ar\" ? \"rtl\" : \"ltr\");\n    };\n    const t = (key)=>{\n        var _translations_language;\n        return ((_translations_language = translations[language]) === null || _translations_language === void 0 ? void 0 : _translations_language[key]) || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n            value: {\n                language,\n                setLanguage: handleSetLanguage,\n                t\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s2(Providers, \"v7iOU9QEBMOWMB4qqRVdgEHG+tI=\");\n_c = Providers;\nvar _c;\n$RefreshReg$(_c, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers.tsx\n"));

/***/ })

});