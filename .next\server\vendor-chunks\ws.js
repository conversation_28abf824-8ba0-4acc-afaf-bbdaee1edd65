"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ws";
exports.ids = ["vendor-chunks/ws"];
exports.modules = {

/***/ "(ssr)/./node_modules/ws/index.js":
/*!**********************************!*\
  !*** ./node_modules/ws/index.js ***!
  \**********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst WebSocket = __webpack_require__(/*! ./lib/websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\n\nWebSocket.createWebSocketStream = __webpack_require__(/*! ./lib/stream */ \"(ssr)/./node_modules/ws/lib/stream.js\");\nWebSocket.Server = __webpack_require__(/*! ./lib/websocket-server */ \"(ssr)/./node_modules/ws/lib/websocket-server.js\");\nWebSocket.Receiver = __webpack_require__(/*! ./lib/receiver */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\nWebSocket.Sender = __webpack_require__(/*! ./lib/sender */ \"(ssr)/./node_modules/ws/lib/sender.js\");\n\nWebSocket.WebSocket = WebSocket;\nWebSocket.WebSocketServer = WebSocket.Server;\n\nmodule.exports = WebSocket;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsa0JBQWtCLG1CQUFPLENBQUMsaUVBQWlCOztBQUUzQyxrQ0FBa0MsbUJBQU8sQ0FBQywyREFBYztBQUN4RCxtQkFBbUIsbUJBQU8sQ0FBQywrRUFBd0I7QUFDbkQscUJBQXFCLG1CQUFPLENBQUMsK0RBQWdCO0FBQzdDLG1CQUFtQixtQkFBTyxDQUFDLDJEQUFjOztBQUV6QztBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zLWd5bS1lbGl0ZS8uL25vZGVfbW9kdWxlcy93cy9pbmRleC5qcz8wZGU1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgV2ViU29ja2V0ID0gcmVxdWlyZSgnLi9saWIvd2Vic29ja2V0Jyk7XG5cbldlYlNvY2tldC5jcmVhdGVXZWJTb2NrZXRTdHJlYW0gPSByZXF1aXJlKCcuL2xpYi9zdHJlYW0nKTtcbldlYlNvY2tldC5TZXJ2ZXIgPSByZXF1aXJlKCcuL2xpYi93ZWJzb2NrZXQtc2VydmVyJyk7XG5XZWJTb2NrZXQuUmVjZWl2ZXIgPSByZXF1aXJlKCcuL2xpYi9yZWNlaXZlcicpO1xuV2ViU29ja2V0LlNlbmRlciA9IHJlcXVpcmUoJy4vbGliL3NlbmRlcicpO1xuXG5XZWJTb2NrZXQuV2ViU29ja2V0ID0gV2ViU29ja2V0O1xuV2ViU29ja2V0LldlYlNvY2tldFNlcnZlciA9IFdlYlNvY2tldC5TZXJ2ZXI7XG5cbm1vZHVsZS5leHBvcnRzID0gV2ViU29ja2V0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/buffer-util.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/buffer-util.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { EMPTY_BUFFER } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n\nconst FastBuffer = Buffer[Symbol.species];\n\n/**\n * Merges an array of buffers into a new buffer.\n *\n * @param {Buffer[]} list The array of buffers to concat\n * @param {Number} totalLength The total length of buffers in the list\n * @return {Buffer} The resulting buffer\n * @public\n */\nfunction concat(list, totalLength) {\n  if (list.length === 0) return EMPTY_BUFFER;\n  if (list.length === 1) return list[0];\n\n  const target = Buffer.allocUnsafe(totalLength);\n  let offset = 0;\n\n  for (let i = 0; i < list.length; i++) {\n    const buf = list[i];\n    target.set(buf, offset);\n    offset += buf.length;\n  }\n\n  if (offset < totalLength) {\n    return new FastBuffer(target.buffer, target.byteOffset, offset);\n  }\n\n  return target;\n}\n\n/**\n * Masks a buffer using the given mask.\n *\n * @param {Buffer} source The buffer to mask\n * @param {Buffer} mask The mask to use\n * @param {Buffer} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */\nfunction _mask(source, mask, output, offset, length) {\n  for (let i = 0; i < length; i++) {\n    output[offset + i] = source[i] ^ mask[i & 3];\n  }\n}\n\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {Buffer} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */\nfunction _unmask(buffer, mask) {\n  for (let i = 0; i < buffer.length; i++) {\n    buffer[i] ^= mask[i & 3];\n  }\n}\n\n/**\n * Converts a buffer to an `ArrayBuffer`.\n *\n * @param {Buffer} buf The buffer to convert\n * @return {ArrayBuffer} Converted buffer\n * @public\n */\nfunction toArrayBuffer(buf) {\n  if (buf.length === buf.buffer.byteLength) {\n    return buf.buffer;\n  }\n\n  return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\n\n/**\n * Converts `data` to a `Buffer`.\n *\n * @param {*} data The data to convert\n * @return {Buffer} The buffer\n * @throws {TypeError}\n * @public\n */\nfunction toBuffer(data) {\n  toBuffer.readOnly = true;\n\n  if (Buffer.isBuffer(data)) return data;\n\n  let buf;\n\n  if (data instanceof ArrayBuffer) {\n    buf = new FastBuffer(data);\n  } else if (ArrayBuffer.isView(data)) {\n    buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);\n  } else {\n    buf = Buffer.from(data);\n    toBuffer.readOnly = false;\n  }\n\n  return buf;\n}\n\nmodule.exports = {\n  concat,\n  mask: _mask,\n  toArrayBuffer,\n  toBuffer,\n  unmask: _unmask\n};\n\n/* istanbul ignore else  */\nif (!process.env.WS_NO_BUFFER_UTIL) {\n  try {\n    const bufferUtil = __webpack_require__(/*! bufferutil */ \"?32c4\");\n\n    module.exports.mask = function (source, mask, output, offset, length) {\n      if (length < 48) _mask(source, mask, output, offset, length);\n      else bufferUtil.mask(source, mask, output, offset, length);\n    };\n\n    module.exports.unmask = function (buffer, mask) {\n      if (buffer.length < 32) _unmask(buffer, mask);\n      else bufferUtil.unmask(buffer, mask);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/buffer-util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/constants.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/constants.js ***!
  \******************************************/
/***/ ((module) => {

eval("\n\nconst BINARY_TYPES = ['nodebuffer', 'arraybuffer', 'fragments'];\nconst hasBlob = typeof Blob !== 'undefined';\n\nif (hasBlob) BINARY_TYPES.push('blob');\n\nmodule.exports = {\n  BINARY_TYPES,\n  EMPTY_BUFFER: Buffer.alloc(0),\n  GUID: '258EAFA5-E914-47DA-95CA-C5AB0DC85B11',\n  hasBlob,\n  kForOnEventAttribute: Symbol('kIsForOnEventAttribute'),\n  kListener: Symbol('kListener'),\n  kStatusCode: Symbol('status-code'),\n  kWebSocket: Symbol('websocket'),\n  NOOP: () => {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3MtZ3ltLWVsaXRlLy4vbm9kZV9tb2R1bGVzL3dzL2xpYi9jb25zdGFudHMuanM/MGExYyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IEJJTkFSWV9UWVBFUyA9IFsnbm9kZWJ1ZmZlcicsICdhcnJheWJ1ZmZlcicsICdmcmFnbWVudHMnXTtcbmNvbnN0IGhhc0Jsb2IgPSB0eXBlb2YgQmxvYiAhPT0gJ3VuZGVmaW5lZCc7XG5cbmlmIChoYXNCbG9iKSBCSU5BUllfVFlQRVMucHVzaCgnYmxvYicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgQklOQVJZX1RZUEVTLFxuICBFTVBUWV9CVUZGRVI6IEJ1ZmZlci5hbGxvYygwKSxcbiAgR1VJRDogJzI1OEVBRkE1LUU5MTQtNDdEQS05NUNBLUM1QUIwREM4NUIxMScsXG4gIGhhc0Jsb2IsXG4gIGtGb3JPbkV2ZW50QXR0cmlidXRlOiBTeW1ib2woJ2tJc0Zvck9uRXZlbnRBdHRyaWJ1dGUnKSxcbiAga0xpc3RlbmVyOiBTeW1ib2woJ2tMaXN0ZW5lcicpLFxuICBrU3RhdHVzQ29kZTogU3ltYm9sKCdzdGF0dXMtY29kZScpLFxuICBrV2ViU29ja2V0OiBTeW1ib2woJ3dlYnNvY2tldCcpLFxuICBOT09QOiAoKSA9PiB7fVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/event-target.js":
/*!*********************************************!*\
  !*** ./node_modules/ws/lib/event-target.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { kForOnEventAttribute, kListener } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n\nconst kCode = Symbol('kCode');\nconst kData = Symbol('kData');\nconst kError = Symbol('kError');\nconst kMessage = Symbol('kMessage');\nconst kReason = Symbol('kReason');\nconst kTarget = Symbol('kTarget');\nconst kType = Symbol('kType');\nconst kWasClean = Symbol('kWasClean');\n\n/**\n * Class representing an event.\n */\nclass Event {\n  /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */\n  constructor(type) {\n    this[kTarget] = null;\n    this[kType] = type;\n  }\n\n  /**\n   * @type {*}\n   */\n  get target() {\n    return this[kTarget];\n  }\n\n  /**\n   * @type {String}\n   */\n  get type() {\n    return this[kType];\n  }\n}\n\nObject.defineProperty(Event.prototype, 'target', { enumerable: true });\nObject.defineProperty(Event.prototype, 'type', { enumerable: true });\n\n/**\n * Class representing a close event.\n *\n * @extends Event\n */\nclass CloseEvent extends Event {\n  /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kCode] = options.code === undefined ? 0 : options.code;\n    this[kReason] = options.reason === undefined ? '' : options.reason;\n    this[kWasClean] = options.wasClean === undefined ? false : options.wasClean;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get code() {\n    return this[kCode];\n  }\n\n  /**\n   * @type {String}\n   */\n  get reason() {\n    return this[kReason];\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get wasClean() {\n    return this[kWasClean];\n  }\n}\n\nObject.defineProperty(CloseEvent.prototype, 'code', { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, 'reason', { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, 'wasClean', { enumerable: true });\n\n/**\n * Class representing an error event.\n *\n * @extends Event\n */\nclass ErrorEvent extends Event {\n  /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kError] = options.error === undefined ? null : options.error;\n    this[kMessage] = options.message === undefined ? '' : options.message;\n  }\n\n  /**\n   * @type {*}\n   */\n  get error() {\n    return this[kError];\n  }\n\n  /**\n   * @type {String}\n   */\n  get message() {\n    return this[kMessage];\n  }\n}\n\nObject.defineProperty(ErrorEvent.prototype, 'error', { enumerable: true });\nObject.defineProperty(ErrorEvent.prototype, 'message', { enumerable: true });\n\n/**\n * Class representing a message event.\n *\n * @extends Event\n */\nclass MessageEvent extends Event {\n  /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kData] = options.data === undefined ? null : options.data;\n  }\n\n  /**\n   * @type {*}\n   */\n  get data() {\n    return this[kData];\n  }\n}\n\nObject.defineProperty(MessageEvent.prototype, 'data', { enumerable: true });\n\n/**\n * This provides methods for emulating the `EventTarget` interface. It's not\n * meant to be used directly.\n *\n * @mixin\n */\nconst EventTarget = {\n  /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */\n  addEventListener(type, handler, options = {}) {\n    for (const listener of this.listeners(type)) {\n      if (\n        !options[kForOnEventAttribute] &&\n        listener[kListener] === handler &&\n        !listener[kForOnEventAttribute]\n      ) {\n        return;\n      }\n    }\n\n    let wrapper;\n\n    if (type === 'message') {\n      wrapper = function onMessage(data, isBinary) {\n        const event = new MessageEvent('message', {\n          data: isBinary ? data : data.toString()\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'close') {\n      wrapper = function onClose(code, message) {\n        const event = new CloseEvent('close', {\n          code,\n          reason: message.toString(),\n          wasClean: this._closeFrameReceived && this._closeFrameSent\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'error') {\n      wrapper = function onError(error) {\n        const event = new ErrorEvent('error', {\n          error,\n          message: error.message\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'open') {\n      wrapper = function onOpen() {\n        const event = new Event('open');\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else {\n      return;\n    }\n\n    wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];\n    wrapper[kListener] = handler;\n\n    if (options.once) {\n      this.once(type, wrapper);\n    } else {\n      this.on(type, wrapper);\n    }\n  },\n\n  /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */\n  removeEventListener(type, handler) {\n    for (const listener of this.listeners(type)) {\n      if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n        this.removeListener(type, listener);\n        break;\n      }\n    }\n  }\n};\n\nmodule.exports = {\n  CloseEvent,\n  ErrorEvent,\n  Event,\n  EventTarget,\n  MessageEvent\n};\n\n/**\n * Call an event listener\n *\n * @param {(Function|Object)} listener The listener to call\n * @param {*} thisArg The value to use as `this`` when calling the listener\n * @param {Event} event The event to pass to the listener\n * @private\n */\nfunction callListener(listener, thisArg, event) {\n  if (typeof listener === 'object' && listener.handleEvent) {\n    listener.handleEvent.call(listener, event);\n  } else {\n    listener.call(thisArg, event);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/event-target.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/extension.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/extension.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { tokenChars } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n\n/**\n * Adds an offer to the map of extension offers or a parameter to the map of\n * parameters.\n *\n * @param {Object} dest The map of extension offers or parameters\n * @param {String} name The extension or parameter name\n * @param {(Object|Boolean|String)} elem The extension parameters or the\n *     parameter value\n * @private\n */\nfunction push(dest, name, elem) {\n  if (dest[name] === undefined) dest[name] = [elem];\n  else dest[name].push(elem);\n}\n\n/**\n * Parses the `Sec-WebSocket-Extensions` header into an object.\n *\n * @param {String} header The field value of the header\n * @return {Object} The parsed object\n * @public\n */\nfunction parse(header) {\n  const offers = Object.create(null);\n  let params = Object.create(null);\n  let mustUnescape = false;\n  let isEscaping = false;\n  let inQuotes = false;\n  let extensionName;\n  let paramName;\n  let start = -1;\n  let code = -1;\n  let end = -1;\n  let i = 0;\n\n  for (; i < header.length; i++) {\n    code = header.charCodeAt(i);\n\n    if (extensionName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (\n        i !== 0 &&\n        (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */\n      ) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b /* ';' */ || code === 0x2c /* ',' */) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        const name = header.slice(start, end);\n        if (code === 0x2c) {\n          push(offers, name, params);\n          params = Object.create(null);\n        } else {\n          extensionName = name;\n        }\n\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else if (paramName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (code === 0x20 || code === 0x09) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        push(params, header.slice(start, end), true);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n\n        start = end = -1;\n      } else if (code === 0x3d /* '=' */ && start !== -1 && end === -1) {\n        paramName = header.slice(start, i);\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else {\n      //\n      // The value of a quoted-string after unescaping must conform to the\n      // token ABNF, so only token characters are valid.\n      // Ref: https://tools.ietf.org/html/rfc6455#section-9.1\n      //\n      if (isEscaping) {\n        if (tokenChars[code] !== 1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (start === -1) start = i;\n        else if (!mustUnescape) mustUnescape = true;\n        isEscaping = false;\n      } else if (inQuotes) {\n        if (tokenChars[code] === 1) {\n          if (start === -1) start = i;\n        } else if (code === 0x22 /* '\"' */ && start !== -1) {\n          inQuotes = false;\n          end = i;\n        } else if (code === 0x5c /* '\\' */) {\n          isEscaping = true;\n        } else {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n      } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3d) {\n        inQuotes = true;\n      } else if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (start !== -1 && (code === 0x20 || code === 0x09)) {\n        if (end === -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        let value = header.slice(start, end);\n        if (mustUnescape) {\n          value = value.replace(/\\\\/g, '');\n          mustUnescape = false;\n        }\n        push(params, paramName, value);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n\n        paramName = undefined;\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    }\n  }\n\n  if (start === -1 || inQuotes || code === 0x20 || code === 0x09) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n\n  if (end === -1) end = i;\n  const token = header.slice(start, end);\n  if (extensionName === undefined) {\n    push(offers, token, params);\n  } else {\n    if (paramName === undefined) {\n      push(params, token, true);\n    } else if (mustUnescape) {\n      push(params, paramName, token.replace(/\\\\/g, ''));\n    } else {\n      push(params, paramName, token);\n    }\n    push(offers, extensionName, params);\n  }\n\n  return offers;\n}\n\n/**\n * Builds the `Sec-WebSocket-Extensions` header field value.\n *\n * @param {Object} extensions The map of extensions and parameters to format\n * @return {String} A string representing the given object\n * @public\n */\nfunction format(extensions) {\n  return Object.keys(extensions)\n    .map((extension) => {\n      let configurations = extensions[extension];\n      if (!Array.isArray(configurations)) configurations = [configurations];\n      return configurations\n        .map((params) => {\n          return [extension]\n            .concat(\n              Object.keys(params).map((k) => {\n                let values = params[k];\n                if (!Array.isArray(values)) values = [values];\n                return values\n                  .map((v) => (v === true ? k : `${k}=${v}`))\n                  .join('; ');\n              })\n            )\n            .join('; ');\n        })\n        .join(', ');\n    })\n    .join(', ');\n}\n\nmodule.exports = { format, parse };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/extension.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/limiter.js":
/*!****************************************!*\
  !*** ./node_modules/ws/lib/limiter.js ***!
  \****************************************/
/***/ ((module) => {

eval("\n\nconst kDone = Symbol('kDone');\nconst kRun = Symbol('kRun');\n\n/**\n * A very simple job queue with adjustable concurrency. Adapted from\n * https://github.com/STRML/async-limiter\n */\nclass Limiter {\n  /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */\n  constructor(concurrency) {\n    this[kDone] = () => {\n      this.pending--;\n      this[kRun]();\n    };\n    this.concurrency = concurrency || Infinity;\n    this.jobs = [];\n    this.pending = 0;\n  }\n\n  /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */\n  add(job) {\n    this.jobs.push(job);\n    this[kRun]();\n  }\n\n  /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */\n  [kRun]() {\n    if (this.pending === this.concurrency) return;\n\n    if (this.jobs.length) {\n      const job = this.jobs.shift();\n\n      this.pending++;\n      job(this[kDone]);\n    }\n  }\n}\n\nmodule.exports = Limiter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/limiter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/permessage-deflate.js":
/*!***************************************************!*\
  !*** ./node_modules/ws/lib/permessage-deflate.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\n\nconst bufferUtil = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst Limiter = __webpack_require__(/*! ./limiter */ \"(ssr)/./node_modules/ws/lib/limiter.js\");\nconst { kStatusCode } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n\nconst FastBuffer = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([0x00, 0x00, 0xff, 0xff]);\nconst kPerMessageDeflate = Symbol('permessage-deflate');\nconst kTotalLength = Symbol('total-length');\nconst kCallback = Symbol('callback');\nconst kBuffers = Symbol('buffers');\nconst kError = Symbol('error');\n\n//\n// We limit zlib concurrency, which prevents severe memory fragmentation\n// as documented in https://github.com/nodejs/node/issues/8871#issuecomment-250915913\n// and https://github.com/websockets/ws/issues/1202\n//\n// Intentionally global; it's the global thread pool that's an issue.\n//\nlet zlibLimiter;\n\n/**\n * permessage-deflate implementation.\n */\nclass PerMessageDeflate {\n  /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */\n  constructor(options, isServer, maxPayload) {\n    this._maxPayload = maxPayload | 0;\n    this._options = options || {};\n    this._threshold =\n      this._options.threshold !== undefined ? this._options.threshold : 1024;\n    this._isServer = !!isServer;\n    this._deflate = null;\n    this._inflate = null;\n\n    this.params = null;\n\n    if (!zlibLimiter) {\n      const concurrency =\n        this._options.concurrencyLimit !== undefined\n          ? this._options.concurrencyLimit\n          : 10;\n      zlibLimiter = new Limiter(concurrency);\n    }\n  }\n\n  /**\n   * @type {String}\n   */\n  static get extensionName() {\n    return 'permessage-deflate';\n  }\n\n  /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */\n  offer() {\n    const params = {};\n\n    if (this._options.serverNoContextTakeover) {\n      params.server_no_context_takeover = true;\n    }\n    if (this._options.clientNoContextTakeover) {\n      params.client_no_context_takeover = true;\n    }\n    if (this._options.serverMaxWindowBits) {\n      params.server_max_window_bits = this._options.serverMaxWindowBits;\n    }\n    if (this._options.clientMaxWindowBits) {\n      params.client_max_window_bits = this._options.clientMaxWindowBits;\n    } else if (this._options.clientMaxWindowBits == null) {\n      params.client_max_window_bits = true;\n    }\n\n    return params;\n  }\n\n  /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */\n  accept(configurations) {\n    configurations = this.normalizeParams(configurations);\n\n    this.params = this._isServer\n      ? this.acceptAsServer(configurations)\n      : this.acceptAsClient(configurations);\n\n    return this.params;\n  }\n\n  /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */\n  cleanup() {\n    if (this._inflate) {\n      this._inflate.close();\n      this._inflate = null;\n    }\n\n    if (this._deflate) {\n      const callback = this._deflate[kCallback];\n\n      this._deflate.close();\n      this._deflate = null;\n\n      if (callback) {\n        callback(\n          new Error(\n            'The deflate stream was closed while data was being processed'\n          )\n        );\n      }\n    }\n  }\n\n  /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsServer(offers) {\n    const opts = this._options;\n    const accepted = offers.find((params) => {\n      if (\n        (opts.serverNoContextTakeover === false &&\n          params.server_no_context_takeover) ||\n        (params.server_max_window_bits &&\n          (opts.serverMaxWindowBits === false ||\n            (typeof opts.serverMaxWindowBits === 'number' &&\n              opts.serverMaxWindowBits > params.server_max_window_bits))) ||\n        (typeof opts.clientMaxWindowBits === 'number' &&\n          !params.client_max_window_bits)\n      ) {\n        return false;\n      }\n\n      return true;\n    });\n\n    if (!accepted) {\n      throw new Error('None of the extension offers can be accepted');\n    }\n\n    if (opts.serverNoContextTakeover) {\n      accepted.server_no_context_takeover = true;\n    }\n    if (opts.clientNoContextTakeover) {\n      accepted.client_no_context_takeover = true;\n    }\n    if (typeof opts.serverMaxWindowBits === 'number') {\n      accepted.server_max_window_bits = opts.serverMaxWindowBits;\n    }\n    if (typeof opts.clientMaxWindowBits === 'number') {\n      accepted.client_max_window_bits = opts.clientMaxWindowBits;\n    } else if (\n      accepted.client_max_window_bits === true ||\n      opts.clientMaxWindowBits === false\n    ) {\n      delete accepted.client_max_window_bits;\n    }\n\n    return accepted;\n  }\n\n  /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsClient(response) {\n    const params = response[0];\n\n    if (\n      this._options.clientNoContextTakeover === false &&\n      params.client_no_context_takeover\n    ) {\n      throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n    }\n\n    if (!params.client_max_window_bits) {\n      if (typeof this._options.clientMaxWindowBits === 'number') {\n        params.client_max_window_bits = this._options.clientMaxWindowBits;\n      }\n    } else if (\n      this._options.clientMaxWindowBits === false ||\n      (typeof this._options.clientMaxWindowBits === 'number' &&\n        params.client_max_window_bits > this._options.clientMaxWindowBits)\n    ) {\n      throw new Error(\n        'Unexpected or invalid parameter \"client_max_window_bits\"'\n      );\n    }\n\n    return params;\n  }\n\n  /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */\n  normalizeParams(configurations) {\n    configurations.forEach((params) => {\n      Object.keys(params).forEach((key) => {\n        let value = params[key];\n\n        if (value.length > 1) {\n          throw new Error(`Parameter \"${key}\" must have only a single value`);\n        }\n\n        value = value[0];\n\n        if (key === 'client_max_window_bits') {\n          if (value !== true) {\n            const num = +value;\n            if (!Number.isInteger(num) || num < 8 || num > 15) {\n              throw new TypeError(\n                `Invalid value for parameter \"${key}\": ${value}`\n              );\n            }\n            value = num;\n          } else if (!this._isServer) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else if (key === 'server_max_window_bits') {\n          const num = +value;\n          if (!Number.isInteger(num) || num < 8 || num > 15) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n          value = num;\n        } else if (\n          key === 'client_no_context_takeover' ||\n          key === 'server_no_context_takeover'\n        ) {\n          if (value !== true) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else {\n          throw new Error(`Unknown parameter \"${key}\"`);\n        }\n\n        params[key] = value;\n      });\n    });\n\n    return configurations;\n  }\n\n  /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  decompress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._decompress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  compress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._compress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _decompress(data, fin, callback) {\n    const endpoint = this._isServer ? 'client' : 'server';\n\n    if (!this._inflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits =\n        typeof this.params[key] !== 'number'\n          ? zlib.Z_DEFAULT_WINDOWBITS\n          : this.params[key];\n\n      this._inflate = zlib.createInflateRaw({\n        ...this._options.zlibInflateOptions,\n        windowBits\n      });\n      this._inflate[kPerMessageDeflate] = this;\n      this._inflate[kTotalLength] = 0;\n      this._inflate[kBuffers] = [];\n      this._inflate.on('error', inflateOnError);\n      this._inflate.on('data', inflateOnData);\n    }\n\n    this._inflate[kCallback] = callback;\n\n    this._inflate.write(data);\n    if (fin) this._inflate.write(TRAILER);\n\n    this._inflate.flush(() => {\n      const err = this._inflate[kError];\n\n      if (err) {\n        this._inflate.close();\n        this._inflate = null;\n        callback(err);\n        return;\n      }\n\n      const data = bufferUtil.concat(\n        this._inflate[kBuffers],\n        this._inflate[kTotalLength]\n      );\n\n      if (this._inflate._readableState.endEmitted) {\n        this._inflate.close();\n        this._inflate = null;\n      } else {\n        this._inflate[kTotalLength] = 0;\n        this._inflate[kBuffers] = [];\n\n        if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n          this._inflate.reset();\n        }\n      }\n\n      callback(null, data);\n    });\n  }\n\n  /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _compress(data, fin, callback) {\n    const endpoint = this._isServer ? 'server' : 'client';\n\n    if (!this._deflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits =\n        typeof this.params[key] !== 'number'\n          ? zlib.Z_DEFAULT_WINDOWBITS\n          : this.params[key];\n\n      this._deflate = zlib.createDeflateRaw({\n        ...this._options.zlibDeflateOptions,\n        windowBits\n      });\n\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n\n      this._deflate.on('data', deflateOnData);\n    }\n\n    this._deflate[kCallback] = callback;\n\n    this._deflate.write(data);\n    this._deflate.flush(zlib.Z_SYNC_FLUSH, () => {\n      if (!this._deflate) {\n        //\n        // The deflate stream was closed while data was being processed.\n        //\n        return;\n      }\n\n      let data = bufferUtil.concat(\n        this._deflate[kBuffers],\n        this._deflate[kTotalLength]\n      );\n\n      if (fin) {\n        data = new FastBuffer(data.buffer, data.byteOffset, data.length - 4);\n      }\n\n      //\n      // Ensure that the callback will not be called again in\n      // `PerMessageDeflate#cleanup()`.\n      //\n      this._deflate[kCallback] = null;\n\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n\n      if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n        this._deflate.reset();\n      }\n\n      callback(null, data);\n    });\n  }\n}\n\nmodule.exports = PerMessageDeflate;\n\n/**\n * The listener of the `zlib.DeflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction deflateOnData(chunk) {\n  this[kBuffers].push(chunk);\n  this[kTotalLength] += chunk.length;\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction inflateOnData(chunk) {\n  this[kTotalLength] += chunk.length;\n\n  if (\n    this[kPerMessageDeflate]._maxPayload < 1 ||\n    this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload\n  ) {\n    this[kBuffers].push(chunk);\n    return;\n  }\n\n  this[kError] = new RangeError('Max payload size exceeded');\n  this[kError].code = 'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH';\n  this[kError][kStatusCode] = 1009;\n  this.removeListener('data', inflateOnData);\n\n  //\n  // The choice to employ `zlib.reset()` over `zlib.close()` is dictated by the\n  // fact that in Node.js versions prior to 13.10.0, the callback for\n  // `zlib.flush()` is not called if `zlib.close()` is used. Utilizing\n  // `zlib.reset()` ensures that either the callback is invoked or an error is\n  // emitted.\n  //\n  this.reset();\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'error'` event.\n *\n * @param {Error} err The emitted error\n * @private\n */\nfunction inflateOnError(err) {\n  //\n  // There is no need to call `Zlib#close()` as the handle is automatically\n  // closed when an error is emitted.\n  //\n  this[kPerMessageDeflate]._inflate = null;\n\n  if (this[kError]) {\n    this[kCallback](this[kError]);\n    return;\n  }\n\n  err[kStatusCode] = 1007;\n  this[kCallback](err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/permessage-deflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/receiver.js":
/*!*****************************************!*\
  !*** ./node_modules/ws/lib/receiver.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { Writable } = __webpack_require__(/*! stream */ \"stream\");\n\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  kStatusCode,\n  kWebSocket\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { concat, toArrayBuffer, unmask } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst { isValidStatusCode, isValidUTF8 } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n\nconst FastBuffer = Buffer[Symbol.species];\n\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nconst DEFER_EVENT = 6;\n\n/**\n * HyBi Receiver implementation.\n *\n * @extends Writable\n */\nclass Receiver extends Writable {\n  /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */\n  constructor(options = {}) {\n    super();\n\n    this._allowSynchronousEvents =\n      options.allowSynchronousEvents !== undefined\n        ? options.allowSynchronousEvents\n        : true;\n    this._binaryType = options.binaryType || BINARY_TYPES[0];\n    this._extensions = options.extensions || {};\n    this._isServer = !!options.isServer;\n    this._maxPayload = options.maxPayload | 0;\n    this._skipUTF8Validation = !!options.skipUTF8Validation;\n    this[kWebSocket] = undefined;\n\n    this._bufferedBytes = 0;\n    this._buffers = [];\n\n    this._compressed = false;\n    this._payloadLength = 0;\n    this._mask = undefined;\n    this._fragmented = 0;\n    this._masked = false;\n    this._fin = false;\n    this._opcode = 0;\n\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragments = [];\n\n    this._errored = false;\n    this._loop = false;\n    this._state = GET_INFO;\n  }\n\n  /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */\n  _write(chunk, encoding, cb) {\n    if (this._opcode === 0x08 && this._state == GET_INFO) return cb();\n\n    this._bufferedBytes += chunk.length;\n    this._buffers.push(chunk);\n    this.startLoop(cb);\n  }\n\n  /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */\n  consume(n) {\n    this._bufferedBytes -= n;\n\n    if (n === this._buffers[0].length) return this._buffers.shift();\n\n    if (n < this._buffers[0].length) {\n      const buf = this._buffers[0];\n      this._buffers[0] = new FastBuffer(\n        buf.buffer,\n        buf.byteOffset + n,\n        buf.length - n\n      );\n\n      return new FastBuffer(buf.buffer, buf.byteOffset, n);\n    }\n\n    const dst = Buffer.allocUnsafe(n);\n\n    do {\n      const buf = this._buffers[0];\n      const offset = dst.length - n;\n\n      if (n >= buf.length) {\n        dst.set(this._buffers.shift(), offset);\n      } else {\n        dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n        this._buffers[0] = new FastBuffer(\n          buf.buffer,\n          buf.byteOffset + n,\n          buf.length - n\n        );\n      }\n\n      n -= buf.length;\n    } while (n > 0);\n\n    return dst;\n  }\n\n  /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  startLoop(cb) {\n    this._loop = true;\n\n    do {\n      switch (this._state) {\n        case GET_INFO:\n          this.getInfo(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_16:\n          this.getPayloadLength16(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_64:\n          this.getPayloadLength64(cb);\n          break;\n        case GET_MASK:\n          this.getMask();\n          break;\n        case GET_DATA:\n          this.getData(cb);\n          break;\n        case INFLATING:\n        case DEFER_EVENT:\n          this._loop = false;\n          return;\n      }\n    } while (this._loop);\n\n    if (!this._errored) cb();\n  }\n\n  /**\n   * Reads the first two bytes of a frame.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getInfo(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n\n    const buf = this.consume(2);\n\n    if ((buf[0] & 0x30) !== 0x00) {\n      const error = this.createError(\n        RangeError,\n        'RSV2 and RSV3 must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_RSV_2_3'\n      );\n\n      cb(error);\n      return;\n    }\n\n    const compressed = (buf[0] & 0x40) === 0x40;\n\n    if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {\n      const error = this.createError(\n        RangeError,\n        'RSV1 must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_RSV_1'\n      );\n\n      cb(error);\n      return;\n    }\n\n    this._fin = (buf[0] & 0x80) === 0x80;\n    this._opcode = buf[0] & 0x0f;\n    this._payloadLength = buf[1] & 0x7f;\n\n    if (this._opcode === 0x00) {\n      if (compressed) {\n        const error = this.createError(\n          RangeError,\n          'RSV1 must be clear',\n          true,\n          1002,\n          'WS_ERR_UNEXPECTED_RSV_1'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (!this._fragmented) {\n        const error = this.createError(\n          RangeError,\n          'invalid opcode 0',\n          true,\n          1002,\n          'WS_ERR_INVALID_OPCODE'\n        );\n\n        cb(error);\n        return;\n      }\n\n      this._opcode = this._fragmented;\n    } else if (this._opcode === 0x01 || this._opcode === 0x02) {\n      if (this._fragmented) {\n        const error = this.createError(\n          RangeError,\n          `invalid opcode ${this._opcode}`,\n          true,\n          1002,\n          'WS_ERR_INVALID_OPCODE'\n        );\n\n        cb(error);\n        return;\n      }\n\n      this._compressed = compressed;\n    } else if (this._opcode > 0x07 && this._opcode < 0x0b) {\n      if (!this._fin) {\n        const error = this.createError(\n          RangeError,\n          'FIN must be set',\n          true,\n          1002,\n          'WS_ERR_EXPECTED_FIN'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (compressed) {\n        const error = this.createError(\n          RangeError,\n          'RSV1 must be clear',\n          true,\n          1002,\n          'WS_ERR_UNEXPECTED_RSV_1'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (\n        this._payloadLength > 0x7d ||\n        (this._opcode === 0x08 && this._payloadLength === 1)\n      ) {\n        const error = this.createError(\n          RangeError,\n          `invalid payload length ${this._payloadLength}`,\n          true,\n          1002,\n          'WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH'\n        );\n\n        cb(error);\n        return;\n      }\n    } else {\n      const error = this.createError(\n        RangeError,\n        `invalid opcode ${this._opcode}`,\n        true,\n        1002,\n        'WS_ERR_INVALID_OPCODE'\n      );\n\n      cb(error);\n      return;\n    }\n\n    if (!this._fin && !this._fragmented) this._fragmented = this._opcode;\n    this._masked = (buf[1] & 0x80) === 0x80;\n\n    if (this._isServer) {\n      if (!this._masked) {\n        const error = this.createError(\n          RangeError,\n          'MASK must be set',\n          true,\n          1002,\n          'WS_ERR_EXPECTED_MASK'\n        );\n\n        cb(error);\n        return;\n      }\n    } else if (this._masked) {\n      const error = this.createError(\n        RangeError,\n        'MASK must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_MASK'\n      );\n\n      cb(error);\n      return;\n    }\n\n    if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;\n    else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;\n    else this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+16).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength16(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n\n    this._payloadLength = this.consume(2).readUInt16BE(0);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+64).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength64(cb) {\n    if (this._bufferedBytes < 8) {\n      this._loop = false;\n      return;\n    }\n\n    const buf = this.consume(8);\n    const num = buf.readUInt32BE(0);\n\n    //\n    // The maximum safe integer in JavaScript is 2^53 - 1. An error is returned\n    // if payload length is greater than this number.\n    //\n    if (num > Math.pow(2, 53 - 32) - 1) {\n      const error = this.createError(\n        RangeError,\n        'Unsupported WebSocket frame: payload length > 2^53 - 1',\n        false,\n        1009,\n        'WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH'\n      );\n\n      cb(error);\n      return;\n    }\n\n    this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Payload length has been read.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  haveLength(cb) {\n    if (this._payloadLength && this._opcode < 0x08) {\n      this._totalPayloadLength += this._payloadLength;\n      if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n        const error = this.createError(\n          RangeError,\n          'Max payload size exceeded',\n          false,\n          1009,\n          'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH'\n        );\n\n        cb(error);\n        return;\n      }\n    }\n\n    if (this._masked) this._state = GET_MASK;\n    else this._state = GET_DATA;\n  }\n\n  /**\n   * Reads mask bytes.\n   *\n   * @private\n   */\n  getMask() {\n    if (this._bufferedBytes < 4) {\n      this._loop = false;\n      return;\n    }\n\n    this._mask = this.consume(4);\n    this._state = GET_DATA;\n  }\n\n  /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getData(cb) {\n    let data = EMPTY_BUFFER;\n\n    if (this._payloadLength) {\n      if (this._bufferedBytes < this._payloadLength) {\n        this._loop = false;\n        return;\n      }\n\n      data = this.consume(this._payloadLength);\n\n      if (\n        this._masked &&\n        (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0\n      ) {\n        unmask(data, this._mask);\n      }\n    }\n\n    if (this._opcode > 0x07) {\n      this.controlMessage(data, cb);\n      return;\n    }\n\n    if (this._compressed) {\n      this._state = INFLATING;\n      this.decompress(data, cb);\n      return;\n    }\n\n    if (data.length) {\n      //\n      // This message is not compressed so its length is the sum of the payload\n      // length of all fragments.\n      //\n      this._messageLength = this._totalPayloadLength;\n      this._fragments.push(data);\n    }\n\n    this.dataMessage(cb);\n  }\n\n  /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */\n  decompress(data, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n\n    perMessageDeflate.decompress(data, this._fin, (err, buf) => {\n      if (err) return cb(err);\n\n      if (buf.length) {\n        this._messageLength += buf.length;\n        if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n          const error = this.createError(\n            RangeError,\n            'Max payload size exceeded',\n            false,\n            1009,\n            'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH'\n          );\n\n          cb(error);\n          return;\n        }\n\n        this._fragments.push(buf);\n      }\n\n      this.dataMessage(cb);\n      if (this._state === GET_INFO) this.startLoop(cb);\n    });\n  }\n\n  /**\n   * Handles a data message.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  dataMessage(cb) {\n    if (!this._fin) {\n      this._state = GET_INFO;\n      return;\n    }\n\n    const messageLength = this._messageLength;\n    const fragments = this._fragments;\n\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragmented = 0;\n    this._fragments = [];\n\n    if (this._opcode === 2) {\n      let data;\n\n      if (this._binaryType === 'nodebuffer') {\n        data = concat(fragments, messageLength);\n      } else if (this._binaryType === 'arraybuffer') {\n        data = toArrayBuffer(concat(fragments, messageLength));\n      } else if (this._binaryType === 'blob') {\n        data = new Blob(fragments);\n      } else {\n        data = fragments;\n      }\n\n      if (this._allowSynchronousEvents) {\n        this.emit('message', data, true);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', data, true);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    } else {\n      const buf = concat(fragments, messageLength);\n\n      if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n        const error = this.createError(\n          Error,\n          'invalid UTF-8 sequence',\n          true,\n          1007,\n          'WS_ERR_INVALID_UTF8'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (this._state === INFLATING || this._allowSynchronousEvents) {\n        this.emit('message', buf, false);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', buf, false);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    }\n  }\n\n  /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  controlMessage(data, cb) {\n    if (this._opcode === 0x08) {\n      if (data.length === 0) {\n        this._loop = false;\n        this.emit('conclude', 1005, EMPTY_BUFFER);\n        this.end();\n      } else {\n        const code = data.readUInt16BE(0);\n\n        if (!isValidStatusCode(code)) {\n          const error = this.createError(\n            RangeError,\n            `invalid status code ${code}`,\n            true,\n            1002,\n            'WS_ERR_INVALID_CLOSE_CODE'\n          );\n\n          cb(error);\n          return;\n        }\n\n        const buf = new FastBuffer(\n          data.buffer,\n          data.byteOffset + 2,\n          data.length - 2\n        );\n\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          const error = this.createError(\n            Error,\n            'invalid UTF-8 sequence',\n            true,\n            1007,\n            'WS_ERR_INVALID_UTF8'\n          );\n\n          cb(error);\n          return;\n        }\n\n        this._loop = false;\n        this.emit('conclude', code, buf);\n        this.end();\n      }\n\n      this._state = GET_INFO;\n      return;\n    }\n\n    if (this._allowSynchronousEvents) {\n      this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n      this._state = GET_INFO;\n    } else {\n      this._state = DEFER_EVENT;\n      setImmediate(() => {\n        this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n        this._state = GET_INFO;\n        this.startLoop(cb);\n      });\n    }\n  }\n\n  /**\n   * Builds an error object.\n   *\n   * @param {function(new:Error|RangeError)} ErrorCtor The error constructor\n   * @param {String} message The error message\n   * @param {Boolean} prefix Specifies whether or not to add a default prefix to\n   *     `message`\n   * @param {Number} statusCode The status code\n   * @param {String} errorCode The exposed error code\n   * @return {(Error|RangeError)} The error\n   * @private\n   */\n  createError(ErrorCtor, message, prefix, statusCode, errorCode) {\n    this._loop = false;\n    this._errored = true;\n\n    const err = new ErrorCtor(\n      prefix ? `Invalid WebSocket frame: ${message}` : message\n    );\n\n    Error.captureStackTrace(err, this.createError);\n    err.code = errorCode;\n    err[kStatusCode] = statusCode;\n    return err;\n  }\n}\n\nmodule.exports = Receiver;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/receiver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/sender.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/sender.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex\" }] */ \nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { randomFillSync } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst { EMPTY_BUFFER, kWebSocket, NOOP } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { isBlob, isValidStatusCode } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst { mask: applyMask, toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst kByteLength = Symbol(\"kByteLength\");\nconst maskBuffer = Buffer.alloc(4);\nconst RANDOM_POOL_SIZE = 8 * 1024;\nlet randomPool;\nlet randomPoolPointer = RANDOM_POOL_SIZE;\nconst DEFAULT = 0;\nconst DEFLATING = 1;\nconst GET_BLOB_DATA = 2;\n/**\n * HyBi Sender implementation.\n */ class Sender {\n    /**\n   * Creates a Sender instance.\n   *\n   * @param {Duplex} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */ constructor(socket, extensions, generateMask){\n        this._extensions = extensions || {};\n        if (generateMask) {\n            this._generateMask = generateMask;\n            this._maskBuffer = Buffer.alloc(4);\n        }\n        this._socket = socket;\n        this._firstFragment = true;\n        this._compress = false;\n        this._bufferedBytes = 0;\n        this._queue = [];\n        this._state = DEFAULT;\n        this.onerror = NOOP;\n        this[kWebSocket] = undefined;\n    }\n    /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */ static frame(data, options) {\n        let mask;\n        let merge = false;\n        let offset = 2;\n        let skipMasking = false;\n        if (options.mask) {\n            mask = options.maskBuffer || maskBuffer;\n            if (options.generateMask) {\n                options.generateMask(mask);\n            } else {\n                if (randomPoolPointer === RANDOM_POOL_SIZE) {\n                    /* istanbul ignore else  */ if (randomPool === undefined) {\n                        //\n                        // This is lazily initialized because server-sent frames must not\n                        // be masked so it may never be used.\n                        //\n                        randomPool = Buffer.alloc(RANDOM_POOL_SIZE);\n                    }\n                    randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);\n                    randomPoolPointer = 0;\n                }\n                mask[0] = randomPool[randomPoolPointer++];\n                mask[1] = randomPool[randomPoolPointer++];\n                mask[2] = randomPool[randomPoolPointer++];\n                mask[3] = randomPool[randomPoolPointer++];\n            }\n            skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;\n            offset = 6;\n        }\n        let dataLength;\n        if (typeof data === \"string\") {\n            if ((!options.mask || skipMasking) && options[kByteLength] !== undefined) {\n                dataLength = options[kByteLength];\n            } else {\n                data = Buffer.from(data);\n                dataLength = data.length;\n            }\n        } else {\n            dataLength = data.length;\n            merge = options.mask && options.readOnly && !skipMasking;\n        }\n        let payloadLength = dataLength;\n        if (dataLength >= 65536) {\n            offset += 8;\n            payloadLength = 127;\n        } else if (dataLength > 125) {\n            offset += 2;\n            payloadLength = 126;\n        }\n        const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n        target[0] = options.fin ? options.opcode | 0x80 : options.opcode;\n        if (options.rsv1) target[0] |= 0x40;\n        target[1] = payloadLength;\n        if (payloadLength === 126) {\n            target.writeUInt16BE(dataLength, 2);\n        } else if (payloadLength === 127) {\n            target[2] = target[3] = 0;\n            target.writeUIntBE(dataLength, 4, 6);\n        }\n        if (!options.mask) return [\n            target,\n            data\n        ];\n        target[1] |= 0x80;\n        target[offset - 4] = mask[0];\n        target[offset - 3] = mask[1];\n        target[offset - 2] = mask[2];\n        target[offset - 1] = mask[3];\n        if (skipMasking) return [\n            target,\n            data\n        ];\n        if (merge) {\n            applyMask(data, mask, target, offset, dataLength);\n            return [\n                target\n            ];\n        }\n        applyMask(data, mask, data, 0, dataLength);\n        return [\n            target,\n            data\n        ];\n    }\n    /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */ close(code, data, mask, cb) {\n        let buf;\n        if (code === undefined) {\n            buf = EMPTY_BUFFER;\n        } else if (typeof code !== \"number\" || !isValidStatusCode(code)) {\n            throw new TypeError(\"First argument must be a valid error code number\");\n        } else if (data === undefined || !data.length) {\n            buf = Buffer.allocUnsafe(2);\n            buf.writeUInt16BE(code, 0);\n        } else {\n            const length = Buffer.byteLength(data);\n            if (length > 123) {\n                throw new RangeError(\"The message must not be greater than 123 bytes\");\n            }\n            buf = Buffer.allocUnsafe(2 + length);\n            buf.writeUInt16BE(code, 0);\n            if (typeof data === \"string\") {\n                buf.write(data, 2);\n            } else {\n                buf.set(data, 2);\n            }\n        }\n        const options = {\n            [kByteLength]: buf.length,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x08,\n            readOnly: false,\n            rsv1: false\n        };\n        if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                buf,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(buf, options), cb);\n        }\n    }\n    /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ ping(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError(\"The data size must not be greater than 125 bytes\");\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x09,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ pong(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError(\"The data size must not be greater than 125 bytes\");\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x0a,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ send(data, options, cb) {\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        let opcode = options.binary ? 2 : 1;\n        let rsv1 = options.compress;\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (this._firstFragment) {\n            this._firstFragment = false;\n            if (rsv1 && perMessageDeflate && perMessageDeflate.params[perMessageDeflate._isServer ? \"server_no_context_takeover\" : \"client_no_context_takeover\"]) {\n                rsv1 = byteLength >= perMessageDeflate._threshold;\n            }\n            this._compress = rsv1;\n        } else {\n            rsv1 = false;\n            opcode = 0;\n        }\n        if (options.fin) this._firstFragment = true;\n        const opts = {\n            [kByteLength]: byteLength,\n            fin: options.fin,\n            generateMask: this._generateMask,\n            mask: options.mask,\n            maskBuffer: this._maskBuffer,\n            opcode,\n            readOnly,\n            rsv1\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    this._compress,\n                    opts,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, this._compress, opts, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                this._compress,\n                opts,\n                cb\n            ]);\n        } else {\n            this.dispatch(data, this._compress, opts, cb);\n        }\n    }\n    /**\n   * Gets the contents of a blob as binary data.\n   *\n   * @param {Blob} blob The blob\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     the data\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ getBlobData(blob, compress, options, cb) {\n        this._bufferedBytes += options[kByteLength];\n        this._state = GET_BLOB_DATA;\n        blob.arrayBuffer().then((arrayBuffer)=>{\n            if (this._socket.destroyed) {\n                const err = new Error(\"The socket was closed while the blob was being read\");\n                //\n                // `callCallbacks` is called in the next tick to ensure that errors\n                // that might be thrown in the callbacks behave like errors thrown\n                // outside the promise chain.\n                //\n                process.nextTick(callCallbacks, this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            const data = toBuffer(arrayBuffer);\n            if (!compress) {\n                this._state = DEFAULT;\n                this.sendFrame(Sender.frame(data, options), cb);\n                this.dequeue();\n            } else {\n                this.dispatch(data, compress, options, cb);\n            }\n        }).catch((err)=>{\n            //\n            // `onError` is called in the next tick for the same reason that\n            // `callCallbacks` above is.\n            //\n            process.nextTick(onError, this, err, cb);\n        });\n    }\n    /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ dispatch(data, compress, options, cb) {\n        if (!compress) {\n            this.sendFrame(Sender.frame(data, options), cb);\n            return;\n        }\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        this._bufferedBytes += options[kByteLength];\n        this._state = DEFLATING;\n        perMessageDeflate.compress(data, options.fin, (_, buf)=>{\n            if (this._socket.destroyed) {\n                const err = new Error(\"The socket was closed while data was being compressed\");\n                callCallbacks(this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            this._state = DEFAULT;\n            options.readOnly = false;\n            this.sendFrame(Sender.frame(buf, options), cb);\n            this.dequeue();\n        });\n    }\n    /**\n   * Executes queued send operations.\n   *\n   * @private\n   */ dequeue() {\n        while(this._state === DEFAULT && this._queue.length){\n            const params = this._queue.shift();\n            this._bufferedBytes -= params[3][kByteLength];\n            Reflect.apply(params[0], this, params.slice(1));\n        }\n    }\n    /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */ enqueue(params) {\n        this._bufferedBytes += params[3][kByteLength];\n        this._queue.push(params);\n    }\n    /**\n   * Sends a frame.\n   *\n   * @param {(Buffer | String)[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */ sendFrame(list, cb) {\n        if (list.length === 2) {\n            this._socket.cork();\n            this._socket.write(list[0]);\n            this._socket.write(list[1], cb);\n            this._socket.uncork();\n        } else {\n            this._socket.write(list[0], cb);\n        }\n    }\n}\nmodule.exports = Sender;\n/**\n * Calls queued callbacks with an error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error to call the callbacks with\n * @param {Function} [cb] The first callback\n * @private\n */ function callCallbacks(sender, err, cb) {\n    if (typeof cb === \"function\") cb(err);\n    for(let i = 0; i < sender._queue.length; i++){\n        const params = sender._queue[i];\n        const callback = params[params.length - 1];\n        if (typeof callback === \"function\") callback(err);\n    }\n}\n/**\n * Handles a `Sender` error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error\n * @param {Function} [cb] The first pending callback\n * @private\n */ function onError(sender, err, cb) {\n    callCallbacks(sender, err, cb);\n    sender.onerror(err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/sender.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/stream.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/stream.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^WebSocket$\" }] */\n\n\nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\n\n/**\n * Emits the `'close'` event on a stream.\n *\n * @param {Duplex} stream The stream.\n * @private\n */\nfunction emitClose(stream) {\n  stream.emit('close');\n}\n\n/**\n * The listener of the `'end'` event.\n *\n * @private\n */\nfunction duplexOnEnd() {\n  if (!this.destroyed && this._writableState.finished) {\n    this.destroy();\n  }\n}\n\n/**\n * The listener of the `'error'` event.\n *\n * @param {Error} err The error\n * @private\n */\nfunction duplexOnError(err) {\n  this.removeListener('error', duplexOnError);\n  this.destroy();\n  if (this.listenerCount('error') === 0) {\n    // Do not suppress the throwing behavior.\n    this.emit('error', err);\n  }\n}\n\n/**\n * Wraps a `WebSocket` in a duplex stream.\n *\n * @param {WebSocket} ws The `WebSocket` to wrap\n * @param {Object} [options] The options for the `Duplex` constructor\n * @return {Duplex} The duplex stream\n * @public\n */\nfunction createWebSocketStream(ws, options) {\n  let terminateOnDestroy = true;\n\n  const duplex = new Duplex({\n    ...options,\n    autoDestroy: false,\n    emitClose: false,\n    objectMode: false,\n    writableObjectMode: false\n  });\n\n  ws.on('message', function message(msg, isBinary) {\n    const data =\n      !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n\n    if (!duplex.push(data)) ws.pause();\n  });\n\n  ws.once('error', function error(err) {\n    if (duplex.destroyed) return;\n\n    // Prevent `ws.terminate()` from being called by `duplex._destroy()`.\n    //\n    // - If the `'error'` event is emitted before the `'open'` event, then\n    //   `ws.terminate()` is a noop as no socket is assigned.\n    // - Otherwise, the error is re-emitted by the listener of the `'error'`\n    //   event of the `Receiver` object. The listener already closes the\n    //   connection by calling `ws.close()`. This allows a close frame to be\n    //   sent to the other peer. If `ws.terminate()` is called right after this,\n    //   then the close frame might not be sent.\n    terminateOnDestroy = false;\n    duplex.destroy(err);\n  });\n\n  ws.once('close', function close() {\n    if (duplex.destroyed) return;\n\n    duplex.push(null);\n  });\n\n  duplex._destroy = function (err, callback) {\n    if (ws.readyState === ws.CLOSED) {\n      callback(err);\n      process.nextTick(emitClose, duplex);\n      return;\n    }\n\n    let called = false;\n\n    ws.once('error', function error(err) {\n      called = true;\n      callback(err);\n    });\n\n    ws.once('close', function close() {\n      if (!called) callback(err);\n      process.nextTick(emitClose, duplex);\n    });\n\n    if (terminateOnDestroy) ws.terminate();\n  };\n\n  duplex._final = function (callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once('open', function open() {\n        duplex._final(callback);\n      });\n      return;\n    }\n\n    // If the value of the `_socket` property is `null` it means that `ws` is a\n    // client websocket and the handshake failed. In fact, when this happens, a\n    // socket is never assigned to the websocket. Wait for the `'error'` event\n    // that will be emitted by the websocket.\n    if (ws._socket === null) return;\n\n    if (ws._socket._writableState.finished) {\n      callback();\n      if (duplex._readableState.endEmitted) duplex.destroy();\n    } else {\n      ws._socket.once('finish', function finish() {\n        // `duplex` is not destroyed here because the `'end'` event will be\n        // emitted on `duplex` after this `'finish'` event. The EOF signaling\n        // `null` chunk is, in fact, pushed when the websocket emits `'close'`.\n        callback();\n      });\n      ws.close();\n    }\n  };\n\n  duplex._read = function () {\n    if (ws.isPaused) ws.resume();\n  };\n\n  duplex._write = function (chunk, encoding, callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once('open', function open() {\n        duplex._write(chunk, encoding, callback);\n      });\n      return;\n    }\n\n    ws.send(chunk, callback);\n  };\n\n  duplex.on('end', duplexOnEnd);\n  duplex.on('error', duplexOnError);\n  return duplex;\n}\n\nmodule.exports = createWebSocketStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/subprotocol.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/subprotocol.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { tokenChars } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n\n/**\n * Parses the `Sec-WebSocket-Protocol` header into a set of subprotocol names.\n *\n * @param {String} header The field value of the header\n * @return {Set} The subprotocol names\n * @public\n */\nfunction parse(header) {\n  const protocols = new Set();\n  let start = -1;\n  let end = -1;\n  let i = 0;\n\n  for (i; i < header.length; i++) {\n    const code = header.charCodeAt(i);\n\n    if (end === -1 && tokenChars[code] === 1) {\n      if (start === -1) start = i;\n    } else if (\n      i !== 0 &&\n      (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */\n    ) {\n      if (end === -1 && start !== -1) end = i;\n    } else if (code === 0x2c /* ',' */) {\n      if (start === -1) {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n\n      if (end === -1) end = i;\n\n      const protocol = header.slice(start, end);\n\n      if (protocols.has(protocol)) {\n        throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n      }\n\n      protocols.add(protocol);\n      start = end = -1;\n    } else {\n      throw new SyntaxError(`Unexpected character at index ${i}`);\n    }\n  }\n\n  if (start === -1 || end !== -1) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n\n  const protocol = header.slice(start, i);\n\n  if (protocols.has(protocol)) {\n    throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n  }\n\n  protocols.add(protocol);\n  return protocols;\n}\n\nmodule.exports = { parse };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/subprotocol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/validation.js":
/*!*******************************************!*\
  !*** ./node_modules/ws/lib/validation.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { isUtf8 } = __webpack_require__(/*! buffer */ \"buffer\");\n\nconst { hasBlob } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n\n//\n// Allowed token characters:\n//\n// '!', '#', '$', '%', '&', ''', '*', '+', '-',\n// '.', 0-9, A-Z, '^', '_', '`', a-z, '|', '~'\n//\n// tokenChars[32] === 0 // ' '\n// tokenChars[33] === 1 // '!'\n// tokenChars[34] === 0 // '\"'\n// ...\n//\n// prettier-ignore\nconst tokenChars = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 0 - 15\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 16 - 31\n  0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, // 32 - 47\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, // 48 - 63\n  0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 64 - 79\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, // 80 - 95\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 96 - 111\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0 // 112 - 127\n];\n\n/**\n * Checks if a status code is allowed in a close frame.\n *\n * @param {Number} code The status code\n * @return {Boolean} `true` if the status code is valid, else `false`\n * @public\n */\nfunction isValidStatusCode(code) {\n  return (\n    (code >= 1000 &&\n      code <= 1014 &&\n      code !== 1004 &&\n      code !== 1005 &&\n      code !== 1006) ||\n    (code >= 3000 && code <= 4999)\n  );\n}\n\n/**\n * Checks if a given buffer contains only correct UTF-8.\n * Ported from https://www.cl.cam.ac.uk/%7Emgk25/ucs/utf8_check.c by\n * Markus Kuhn.\n *\n * @param {Buffer} buf The buffer to check\n * @return {Boolean} `true` if `buf` contains only correct UTF-8, else `false`\n * @public\n */\nfunction _isValidUTF8(buf) {\n  const len = buf.length;\n  let i = 0;\n\n  while (i < len) {\n    if ((buf[i] & 0x80) === 0) {\n      // 0xxxxxxx\n      i++;\n    } else if ((buf[i] & 0xe0) === 0xc0) {\n      // 110xxxxx 10xxxxxx\n      if (\n        i + 1 === len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i] & 0xfe) === 0xc0 // Overlong\n      ) {\n        return false;\n      }\n\n      i += 2;\n    } else if ((buf[i] & 0xf0) === 0xe0) {\n      // 1110xxxx 10xxxxxx 10xxxxxx\n      if (\n        i + 2 >= len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i + 2] & 0xc0) !== 0x80 ||\n        (buf[i] === 0xe0 && (buf[i + 1] & 0xe0) === 0x80) || // Overlong\n        (buf[i] === 0xed && (buf[i + 1] & 0xe0) === 0xa0) // Surrogate (U+D800 - U+DFFF)\n      ) {\n        return false;\n      }\n\n      i += 3;\n    } else if ((buf[i] & 0xf8) === 0xf0) {\n      // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx\n      if (\n        i + 3 >= len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i + 2] & 0xc0) !== 0x80 ||\n        (buf[i + 3] & 0xc0) !== 0x80 ||\n        (buf[i] === 0xf0 && (buf[i + 1] & 0xf0) === 0x80) || // Overlong\n        (buf[i] === 0xf4 && buf[i + 1] > 0x8f) ||\n        buf[i] > 0xf4 // > U+10FFFF\n      ) {\n        return false;\n      }\n\n      i += 4;\n    } else {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Determines whether a value is a `Blob`.\n *\n * @param {*} value The value to be tested\n * @return {Boolean} `true` if `value` is a `Blob`, else `false`\n * @private\n */\nfunction isBlob(value) {\n  return (\n    hasBlob &&\n    typeof value === 'object' &&\n    typeof value.arrayBuffer === 'function' &&\n    typeof value.type === 'string' &&\n    typeof value.stream === 'function' &&\n    (value[Symbol.toStringTag] === 'Blob' ||\n      value[Symbol.toStringTag] === 'File')\n  );\n}\n\nmodule.exports = {\n  isBlob,\n  isValidStatusCode,\n  isValidUTF8: _isValidUTF8,\n  tokenChars\n};\n\nif (isUtf8) {\n  module.exports.isValidUTF8 = function (buf) {\n    return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n  };\n} /* istanbul ignore else  */ else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n  try {\n    const isValidUTF8 = __webpack_require__(/*! utf-8-validate */ \"?66e9\");\n\n    module.exports.isValidUTF8 = function (buf) {\n      return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/validation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket-server.js":
/*!*************************************************!*\
  !*** ./node_modules/ws/lib/websocket-server.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex$\", \"caughtErrors\": \"none\" }] */\n\n\n\nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { createHash } = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst extension = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst subprotocol = __webpack_require__(/*! ./subprotocol */ \"(ssr)/./node_modules/ws/lib/subprotocol.js\");\nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst { GUID, kWebSocket } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\n\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\n\n/**\n * Class representing a WebSocket server.\n *\n * @extends EventEmitter\n */\nclass WebSocketServer extends EventEmitter {\n  /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n   *     automatically send a pong in response to a ping\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */\n  constructor(options, callback) {\n    super();\n\n    options = {\n      allowSynchronousEvents: true,\n      autoPong: true,\n      maxPayload: 100 * 1024 * 1024,\n      skipUTF8Validation: false,\n      perMessageDeflate: false,\n      handleProtocols: null,\n      clientTracking: true,\n      verifyClient: null,\n      noServer: false,\n      backlog: null, // use default (511 as implemented in net.js)\n      server: null,\n      host: null,\n      path: null,\n      port: null,\n      WebSocket,\n      ...options\n    };\n\n    if (\n      (options.port == null && !options.server && !options.noServer) ||\n      (options.port != null && (options.server || options.noServer)) ||\n      (options.server && options.noServer)\n    ) {\n      throw new TypeError(\n        'One and only one of the \"port\", \"server\", or \"noServer\" options ' +\n          'must be specified'\n      );\n    }\n\n    if (options.port != null) {\n      this._server = http.createServer((req, res) => {\n        const body = http.STATUS_CODES[426];\n\n        res.writeHead(426, {\n          'Content-Length': body.length,\n          'Content-Type': 'text/plain'\n        });\n        res.end(body);\n      });\n      this._server.listen(\n        options.port,\n        options.host,\n        options.backlog,\n        callback\n      );\n    } else if (options.server) {\n      this._server = options.server;\n    }\n\n    if (this._server) {\n      const emitConnection = this.emit.bind(this, 'connection');\n\n      this._removeListeners = addListeners(this._server, {\n        listening: this.emit.bind(this, 'listening'),\n        error: this.emit.bind(this, 'error'),\n        upgrade: (req, socket, head) => {\n          this.handleUpgrade(req, socket, head, emitConnection);\n        }\n      });\n    }\n\n    if (options.perMessageDeflate === true) options.perMessageDeflate = {};\n    if (options.clientTracking) {\n      this.clients = new Set();\n      this._shouldEmitClose = false;\n    }\n\n    this.options = options;\n    this._state = RUNNING;\n  }\n\n  /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */\n  address() {\n    if (this.options.noServer) {\n      throw new Error('The server is operating in \"noServer\" mode');\n    }\n\n    if (!this._server) return null;\n    return this._server.address();\n  }\n\n  /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */\n  close(cb) {\n    if (this._state === CLOSED) {\n      if (cb) {\n        this.once('close', () => {\n          cb(new Error('The server is not running'));\n        });\n      }\n\n      process.nextTick(emitClose, this);\n      return;\n    }\n\n    if (cb) this.once('close', cb);\n\n    if (this._state === CLOSING) return;\n    this._state = CLOSING;\n\n    if (this.options.noServer || this.options.server) {\n      if (this._server) {\n        this._removeListeners();\n        this._removeListeners = this._server = null;\n      }\n\n      if (this.clients) {\n        if (!this.clients.size) {\n          process.nextTick(emitClose, this);\n        } else {\n          this._shouldEmitClose = true;\n        }\n      } else {\n        process.nextTick(emitClose, this);\n      }\n    } else {\n      const server = this._server;\n\n      this._removeListeners();\n      this._removeListeners = this._server = null;\n\n      //\n      // The HTTP/S server was created internally. Close it, and rely on its\n      // `'close'` event.\n      //\n      server.close(() => {\n        emitClose(this);\n      });\n    }\n  }\n\n  /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */\n  shouldHandle(req) {\n    if (this.options.path) {\n      const index = req.url.indexOf('?');\n      const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n\n      if (pathname !== this.options.path) return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */\n  handleUpgrade(req, socket, head, cb) {\n    socket.on('error', socketOnError);\n\n    const key = req.headers['sec-websocket-key'];\n    const upgrade = req.headers.upgrade;\n    const version = +req.headers['sec-websocket-version'];\n\n    if (req.method !== 'GET') {\n      const message = 'Invalid HTTP method';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n      return;\n    }\n\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      const message = 'Invalid Upgrade header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (key === undefined || !keyRegex.test(key)) {\n      const message = 'Missing or invalid Sec-WebSocket-Key header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (version !== 8 && version !== 13) {\n      const message = 'Missing or invalid Sec-WebSocket-Version header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (!this.shouldHandle(req)) {\n      abortHandshake(socket, 400);\n      return;\n    }\n\n    const secWebSocketProtocol = req.headers['sec-websocket-protocol'];\n    let protocols = new Set();\n\n    if (secWebSocketProtocol !== undefined) {\n      try {\n        protocols = subprotocol.parse(secWebSocketProtocol);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Protocol header';\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n\n    const secWebSocketExtensions = req.headers['sec-websocket-extensions'];\n    const extensions = {};\n\n    if (\n      this.options.perMessageDeflate &&\n      secWebSocketExtensions !== undefined\n    ) {\n      const perMessageDeflate = new PerMessageDeflate(\n        this.options.perMessageDeflate,\n        true,\n        this.options.maxPayload\n      );\n\n      try {\n        const offers = extension.parse(secWebSocketExtensions);\n\n        if (offers[PerMessageDeflate.extensionName]) {\n          perMessageDeflate.accept(offers[PerMessageDeflate.extensionName]);\n          extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n        }\n      } catch (err) {\n        const message =\n          'Invalid or unacceptable Sec-WebSocket-Extensions header';\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n\n    //\n    // Optionally call external client verification handler.\n    //\n    if (this.options.verifyClient) {\n      const info = {\n        origin:\n          req.headers[`${version === 8 ? 'sec-websocket-origin' : 'origin'}`],\n        secure: !!(req.socket.authorized || req.socket.encrypted),\n        req\n      };\n\n      if (this.options.verifyClient.length === 2) {\n        this.options.verifyClient(info, (verified, code, message, headers) => {\n          if (!verified) {\n            return abortHandshake(socket, code || 401, message, headers);\n          }\n\n          this.completeUpgrade(\n            extensions,\n            key,\n            protocols,\n            req,\n            socket,\n            head,\n            cb\n          );\n        });\n        return;\n      }\n\n      if (!this.options.verifyClient(info)) return abortHandshake(socket, 401);\n    }\n\n    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n  }\n\n  /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */\n  completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n    //\n    // Destroy the socket if the client has already sent a FIN packet.\n    //\n    if (!socket.readable || !socket.writable) return socket.destroy();\n\n    if (socket[kWebSocket]) {\n      throw new Error(\n        'server.handleUpgrade() was called more than once with the same ' +\n          'socket, possibly due to a misconfiguration'\n      );\n    }\n\n    if (this._state > RUNNING) return abortHandshake(socket, 503);\n\n    const digest = createHash('sha1')\n      .update(key + GUID)\n      .digest('base64');\n\n    const headers = [\n      'HTTP/1.1 101 Switching Protocols',\n      'Upgrade: websocket',\n      'Connection: Upgrade',\n      `Sec-WebSocket-Accept: ${digest}`\n    ];\n\n    const ws = new this.options.WebSocket(null, undefined, this.options);\n\n    if (protocols.size) {\n      //\n      // Optionally call external protocol selection handler.\n      //\n      const protocol = this.options.handleProtocols\n        ? this.options.handleProtocols(protocols, req)\n        : protocols.values().next().value;\n\n      if (protocol) {\n        headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n        ws._protocol = protocol;\n      }\n    }\n\n    if (extensions[PerMessageDeflate.extensionName]) {\n      const params = extensions[PerMessageDeflate.extensionName].params;\n      const value = extension.format({\n        [PerMessageDeflate.extensionName]: [params]\n      });\n      headers.push(`Sec-WebSocket-Extensions: ${value}`);\n      ws._extensions = extensions;\n    }\n\n    //\n    // Allow external modification/inspection of handshake headers.\n    //\n    this.emit('headers', headers, req);\n\n    socket.write(headers.concat('\\r\\n').join('\\r\\n'));\n    socket.removeListener('error', socketOnError);\n\n    ws.setSocket(socket, head, {\n      allowSynchronousEvents: this.options.allowSynchronousEvents,\n      maxPayload: this.options.maxPayload,\n      skipUTF8Validation: this.options.skipUTF8Validation\n    });\n\n    if (this.clients) {\n      this.clients.add(ws);\n      ws.on('close', () => {\n        this.clients.delete(ws);\n\n        if (this._shouldEmitClose && !this.clients.size) {\n          process.nextTick(emitClose, this);\n        }\n      });\n    }\n\n    cb(ws, req);\n  }\n}\n\nmodule.exports = WebSocketServer;\n\n/**\n * Add event listeners on an `EventEmitter` using a map of <event, listener>\n * pairs.\n *\n * @param {EventEmitter} server The event emitter\n * @param {Object.<String, Function>} map The listeners to add\n * @return {Function} A function that will remove the added listeners when\n *     called\n * @private\n */\nfunction addListeners(server, map) {\n  for (const event of Object.keys(map)) server.on(event, map[event]);\n\n  return function removeListeners() {\n    for (const event of Object.keys(map)) {\n      server.removeListener(event, map[event]);\n    }\n  };\n}\n\n/**\n * Emit a `'close'` event on an `EventEmitter`.\n *\n * @param {EventEmitter} server The event emitter\n * @private\n */\nfunction emitClose(server) {\n  server._state = CLOSED;\n  server.emit('close');\n}\n\n/**\n * Handle socket errors.\n *\n * @private\n */\nfunction socketOnError() {\n  this.destroy();\n}\n\n/**\n * Close the connection when preconditions are not fulfilled.\n *\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} [message] The HTTP response body\n * @param {Object} [headers] Additional HTTP response headers\n * @private\n */\nfunction abortHandshake(socket, code, message, headers) {\n  //\n  // The socket is writable unless the user destroyed or ended it before calling\n  // `server.handleUpgrade()` or in the `verifyClient` function, which is a user\n  // error. Handling this does not make much sense as the worst that can happen\n  // is that some of the data written by the user might be discarded due to the\n  // call to `socket.end()` below, which triggers an `'error'` event that in\n  // turn causes the socket to be destroyed.\n  //\n  message = message || http.STATUS_CODES[code];\n  headers = {\n    Connection: 'close',\n    'Content-Type': 'text/html',\n    'Content-Length': Buffer.byteLength(message),\n    ...headers\n  };\n\n  socket.once('finish', socket.destroy);\n\n  socket.end(\n    `HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\\n` +\n      Object.keys(headers)\n        .map((h) => `${h}: ${headers[h]}`)\n        .join('\\r\\n') +\n      '\\r\\n\\r\\n' +\n      message\n  );\n}\n\n/**\n * Emit a `'wsClientError'` event on a `WebSocketServer` if there is at least\n * one listener for it, otherwise call `abortHandshake()`.\n *\n * @param {WebSocketServer} server The WebSocket server\n * @param {http.IncomingMessage} req The request object\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} message The HTTP response body\n * @private\n */\nfunction abortHandshakeOrEmitwsClientError(server, req, socket, code, message) {\n  if (server.listenerCount('wsClientError')) {\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n\n    server.emit('wsClientError', err, socket, req);\n  } else {\n    abortHandshake(socket, code, message);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket-server.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/websocket.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex|Readable$\", \"caughtErrors\": \"none\" }] */\n\n\n\nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst net = __webpack_require__(/*! net */ \"net\");\nconst tls = __webpack_require__(/*! tls */ \"tls\");\nconst { randomBytes, createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst { Duplex, Readable } = __webpack_require__(/*! stream */ \"stream\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\n\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst Receiver = __webpack_require__(/*! ./receiver */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\nconst Sender = __webpack_require__(/*! ./sender */ \"(ssr)/./node_modules/ws/lib/sender.js\");\nconst { isBlob } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  GUID,\n  kForOnEventAttribute,\n  kListener,\n  kStatusCode,\n  kWebSocket,\n  NOOP\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst {\n  EventTarget: { addEventListener, removeEventListener }\n} = __webpack_require__(/*! ./event-target */ \"(ssr)/./node_modules/ws/lib/event-target.js\");\nconst { format, parse } = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst { toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\n\nconst closeTimeout = 30 * 1000;\nconst kAborted = Symbol('kAborted');\nconst protocolVersions = [8, 13];\nconst readyStates = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\n\n/**\n * Class representing a WebSocket.\n *\n * @extends EventEmitter\n */\nclass WebSocket extends EventEmitter {\n  /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */\n  constructor(address, protocols, options) {\n    super();\n\n    this._binaryType = BINARY_TYPES[0];\n    this._closeCode = 1006;\n    this._closeFrameReceived = false;\n    this._closeFrameSent = false;\n    this._closeMessage = EMPTY_BUFFER;\n    this._closeTimer = null;\n    this._errorEmitted = false;\n    this._extensions = {};\n    this._paused = false;\n    this._protocol = '';\n    this._readyState = WebSocket.CONNECTING;\n    this._receiver = null;\n    this._sender = null;\n    this._socket = null;\n\n    if (address !== null) {\n      this._bufferedAmount = 0;\n      this._isServer = false;\n      this._redirects = 0;\n\n      if (protocols === undefined) {\n        protocols = [];\n      } else if (!Array.isArray(protocols)) {\n        if (typeof protocols === 'object' && protocols !== null) {\n          options = protocols;\n          protocols = [];\n        } else {\n          protocols = [protocols];\n        }\n      }\n\n      initAsClient(this, address, protocols, options);\n    } else {\n      this._autoPong = options.autoPong;\n      this._isServer = true;\n    }\n  }\n\n  /**\n   * For historical reasons, the custom \"nodebuffer\" type is used by the default\n   * instead of \"blob\".\n   *\n   * @type {String}\n   */\n  get binaryType() {\n    return this._binaryType;\n  }\n\n  set binaryType(type) {\n    if (!BINARY_TYPES.includes(type)) return;\n\n    this._binaryType = type;\n\n    //\n    // Allow to change `binaryType` on the fly.\n    //\n    if (this._receiver) this._receiver._binaryType = type;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get bufferedAmount() {\n    if (!this._socket) return this._bufferedAmount;\n\n    return this._socket._writableState.length + this._sender._bufferedBytes;\n  }\n\n  /**\n   * @type {String}\n   */\n  get extensions() {\n    return Object.keys(this._extensions).join();\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get isPaused() {\n    return this._paused;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onclose() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onerror() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onopen() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onmessage() {\n    return null;\n  }\n\n  /**\n   * @type {String}\n   */\n  get protocol() {\n    return this._protocol;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get readyState() {\n    return this._readyState;\n  }\n\n  /**\n   * @type {String}\n   */\n  get url() {\n    return this._url;\n  }\n\n  /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */\n  setSocket(socket, head, options) {\n    const receiver = new Receiver({\n      allowSynchronousEvents: options.allowSynchronousEvents,\n      binaryType: this.binaryType,\n      extensions: this._extensions,\n      isServer: this._isServer,\n      maxPayload: options.maxPayload,\n      skipUTF8Validation: options.skipUTF8Validation\n    });\n\n    const sender = new Sender(socket, this._extensions, options.generateMask);\n\n    this._receiver = receiver;\n    this._sender = sender;\n    this._socket = socket;\n\n    receiver[kWebSocket] = this;\n    sender[kWebSocket] = this;\n    socket[kWebSocket] = this;\n\n    receiver.on('conclude', receiverOnConclude);\n    receiver.on('drain', receiverOnDrain);\n    receiver.on('error', receiverOnError);\n    receiver.on('message', receiverOnMessage);\n    receiver.on('ping', receiverOnPing);\n    receiver.on('pong', receiverOnPong);\n\n    sender.onerror = senderOnError;\n\n    //\n    // These methods may not be available if `socket` is just a `Duplex`.\n    //\n    if (socket.setTimeout) socket.setTimeout(0);\n    if (socket.setNoDelay) socket.setNoDelay();\n\n    if (head.length > 0) socket.unshift(head);\n\n    socket.on('close', socketOnClose);\n    socket.on('data', socketOnData);\n    socket.on('end', socketOnEnd);\n    socket.on('error', socketOnError);\n\n    this._readyState = WebSocket.OPEN;\n    this.emit('open');\n  }\n\n  /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */\n  emitClose() {\n    if (!this._socket) {\n      this._readyState = WebSocket.CLOSED;\n      this.emit('close', this._closeCode, this._closeMessage);\n      return;\n    }\n\n    if (this._extensions[PerMessageDeflate.extensionName]) {\n      this._extensions[PerMessageDeflate.extensionName].cleanup();\n    }\n\n    this._receiver.removeAllListeners();\n    this._readyState = WebSocket.CLOSED;\n    this.emit('close', this._closeCode, this._closeMessage);\n  }\n\n  /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */\n  close(code, data) {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n\n    if (this.readyState === WebSocket.CLOSING) {\n      if (\n        this._closeFrameSent &&\n        (this._closeFrameReceived || this._receiver._writableState.errorEmitted)\n      ) {\n        this._socket.end();\n      }\n\n      return;\n    }\n\n    this._readyState = WebSocket.CLOSING;\n    this._sender.close(code, data, !this._isServer, (err) => {\n      //\n      // This error is handled by the `'error'` listener on the socket. We only\n      // want to know if the close frame has been sent here.\n      //\n      if (err) return;\n\n      this._closeFrameSent = true;\n\n      if (\n        this._closeFrameReceived ||\n        this._receiver._writableState.errorEmitted\n      ) {\n        this._socket.end();\n      }\n    });\n\n    setCloseTimer(this);\n  }\n\n  /**\n   * Pause the socket.\n   *\n   * @public\n   */\n  pause() {\n    if (\n      this.readyState === WebSocket.CONNECTING ||\n      this.readyState === WebSocket.CLOSED\n    ) {\n      return;\n    }\n\n    this._paused = true;\n    this._socket.pause();\n  }\n\n  /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */\n  ping(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.ping(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */\n  pong(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.pong(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Resume the socket.\n   *\n   * @public\n   */\n  resume() {\n    if (\n      this.readyState === WebSocket.CONNECTING ||\n      this.readyState === WebSocket.CLOSED\n    ) {\n      return;\n    }\n\n    this._paused = false;\n    if (!this._receiver._writableState.needDrain) this._socket.resume();\n  }\n\n  /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */\n  send(data, options, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof options === 'function') {\n      cb = options;\n      options = {};\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    const opts = {\n      binary: typeof data !== 'string',\n      mask: !this._isServer,\n      compress: true,\n      fin: true,\n      ...options\n    };\n\n    if (!this._extensions[PerMessageDeflate.extensionName]) {\n      opts.compress = false;\n    }\n\n    this._sender.send(data || EMPTY_BUFFER, opts, cb);\n  }\n\n  /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */\n  terminate() {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n\n    if (this._socket) {\n      this._readyState = WebSocket.CLOSING;\n      this._socket.destroy();\n    }\n  }\n}\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n[\n  'binaryType',\n  'bufferedAmount',\n  'extensions',\n  'isPaused',\n  'protocol',\n  'readyState',\n  'url'\n].forEach((property) => {\n  Object.defineProperty(WebSocket.prototype, property, { enumerable: true });\n});\n\n//\n// Add the `onopen`, `onerror`, `onclose`, and `onmessage` attributes.\n// See https://html.spec.whatwg.org/multipage/comms.html#the-websocket-interface\n//\n['open', 'error', 'close', 'message'].forEach((method) => {\n  Object.defineProperty(WebSocket.prototype, `on${method}`, {\n    enumerable: true,\n    get() {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) return listener[kListener];\n      }\n\n      return null;\n    },\n    set(handler) {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) {\n          this.removeListener(method, listener);\n          break;\n        }\n      }\n\n      if (typeof handler !== 'function') return;\n\n      this.addEventListener(method, handler, {\n        [kForOnEventAttribute]: true\n      });\n    }\n  });\n});\n\nWebSocket.prototype.addEventListener = addEventListener;\nWebSocket.prototype.removeEventListener = removeEventListener;\n\nmodule.exports = WebSocket;\n\n/**\n * Initialize a WebSocket client.\n *\n * @param {WebSocket} websocket The client to initialize\n * @param {(String|URL)} address The URL to which to connect\n * @param {Array} protocols The subprotocols\n * @param {Object} [options] Connection options\n * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether any\n *     of the `'message'`, `'ping'`, and `'pong'` events can be emitted multiple\n *     times in the same tick\n * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n *     automatically send a pong in response to a ping\n * @param {Function} [options.finishRequest] A function which can be used to\n *     customize the headers of each http request before it is sent\n * @param {Boolean} [options.followRedirects=false] Whether or not to follow\n *     redirects\n * @param {Function} [options.generateMask] The function used to generate the\n *     masking key\n * @param {Number} [options.handshakeTimeout] Timeout in milliseconds for the\n *     handshake request\n * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n *     size\n * @param {Number} [options.maxRedirects=10] The maximum number of redirects\n *     allowed\n * @param {String} [options.origin] Value of the `Origin` or\n *     `Sec-WebSocket-Origin` header\n * @param {(Boolean|Object)} [options.perMessageDeflate=true] Enable/disable\n *     permessage-deflate\n * @param {Number} [options.protocolVersion=13] Value of the\n *     `Sec-WebSocket-Version` header\n * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n *     not to skip UTF-8 validation for text and close messages\n * @private\n */\nfunction initAsClient(websocket, address, protocols, options) {\n  const opts = {\n    allowSynchronousEvents: true,\n    autoPong: true,\n    protocolVersion: protocolVersions[1],\n    maxPayload: 100 * 1024 * 1024,\n    skipUTF8Validation: false,\n    perMessageDeflate: true,\n    followRedirects: false,\n    maxRedirects: 10,\n    ...options,\n    socketPath: undefined,\n    hostname: undefined,\n    protocol: undefined,\n    timeout: undefined,\n    method: 'GET',\n    host: undefined,\n    path: undefined,\n    port: undefined\n  };\n\n  websocket._autoPong = opts.autoPong;\n\n  if (!protocolVersions.includes(opts.protocolVersion)) {\n    throw new RangeError(\n      `Unsupported protocol version: ${opts.protocolVersion} ` +\n        `(supported versions: ${protocolVersions.join(', ')})`\n    );\n  }\n\n  let parsedUrl;\n\n  if (address instanceof URL) {\n    parsedUrl = address;\n  } else {\n    try {\n      parsedUrl = new URL(address);\n    } catch (e) {\n      throw new SyntaxError(`Invalid URL: ${address}`);\n    }\n  }\n\n  if (parsedUrl.protocol === 'http:') {\n    parsedUrl.protocol = 'ws:';\n  } else if (parsedUrl.protocol === 'https:') {\n    parsedUrl.protocol = 'wss:';\n  }\n\n  websocket._url = parsedUrl.href;\n\n  const isSecure = parsedUrl.protocol === 'wss:';\n  const isIpcUrl = parsedUrl.protocol === 'ws+unix:';\n  let invalidUrlMessage;\n\n  if (parsedUrl.protocol !== 'ws:' && !isSecure && !isIpcUrl) {\n    invalidUrlMessage =\n      'The URL\\'s protocol must be one of \"ws:\", \"wss:\", ' +\n      '\"http:\", \"https:\", or \"ws+unix:\"';\n  } else if (isIpcUrl && !parsedUrl.pathname) {\n    invalidUrlMessage = \"The URL's pathname is empty\";\n  } else if (parsedUrl.hash) {\n    invalidUrlMessage = 'The URL contains a fragment identifier';\n  }\n\n  if (invalidUrlMessage) {\n    const err = new SyntaxError(invalidUrlMessage);\n\n    if (websocket._redirects === 0) {\n      throw err;\n    } else {\n      emitErrorAndClose(websocket, err);\n      return;\n    }\n  }\n\n  const defaultPort = isSecure ? 443 : 80;\n  const key = randomBytes(16).toString('base64');\n  const request = isSecure ? https.request : http.request;\n  const protocolSet = new Set();\n  let perMessageDeflate;\n\n  opts.createConnection =\n    opts.createConnection || (isSecure ? tlsConnect : netConnect);\n  opts.defaultPort = opts.defaultPort || defaultPort;\n  opts.port = parsedUrl.port || defaultPort;\n  opts.host = parsedUrl.hostname.startsWith('[')\n    ? parsedUrl.hostname.slice(1, -1)\n    : parsedUrl.hostname;\n  opts.headers = {\n    ...opts.headers,\n    'Sec-WebSocket-Version': opts.protocolVersion,\n    'Sec-WebSocket-Key': key,\n    Connection: 'Upgrade',\n    Upgrade: 'websocket'\n  };\n  opts.path = parsedUrl.pathname + parsedUrl.search;\n  opts.timeout = opts.handshakeTimeout;\n\n  if (opts.perMessageDeflate) {\n    perMessageDeflate = new PerMessageDeflate(\n      opts.perMessageDeflate !== true ? opts.perMessageDeflate : {},\n      false,\n      opts.maxPayload\n    );\n    opts.headers['Sec-WebSocket-Extensions'] = format({\n      [PerMessageDeflate.extensionName]: perMessageDeflate.offer()\n    });\n  }\n  if (protocols.length) {\n    for (const protocol of protocols) {\n      if (\n        typeof protocol !== 'string' ||\n        !subprotocolRegex.test(protocol) ||\n        protocolSet.has(protocol)\n      ) {\n        throw new SyntaxError(\n          'An invalid or duplicated subprotocol was specified'\n        );\n      }\n\n      protocolSet.add(protocol);\n    }\n\n    opts.headers['Sec-WebSocket-Protocol'] = protocols.join(',');\n  }\n  if (opts.origin) {\n    if (opts.protocolVersion < 13) {\n      opts.headers['Sec-WebSocket-Origin'] = opts.origin;\n    } else {\n      opts.headers.Origin = opts.origin;\n    }\n  }\n  if (parsedUrl.username || parsedUrl.password) {\n    opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n  }\n\n  if (isIpcUrl) {\n    const parts = opts.path.split(':');\n\n    opts.socketPath = parts[0];\n    opts.path = parts[1];\n  }\n\n  let req;\n\n  if (opts.followRedirects) {\n    if (websocket._redirects === 0) {\n      websocket._originalIpc = isIpcUrl;\n      websocket._originalSecure = isSecure;\n      websocket._originalHostOrSocketPath = isIpcUrl\n        ? opts.socketPath\n        : parsedUrl.host;\n\n      const headers = options && options.headers;\n\n      //\n      // Shallow copy the user provided options so that headers can be changed\n      // without mutating the original object.\n      //\n      options = { ...options, headers: {} };\n\n      if (headers) {\n        for (const [key, value] of Object.entries(headers)) {\n          options.headers[key.toLowerCase()] = value;\n        }\n      }\n    } else if (websocket.listenerCount('redirect') === 0) {\n      const isSameHost = isIpcUrl\n        ? websocket._originalIpc\n          ? opts.socketPath === websocket._originalHostOrSocketPath\n          : false\n        : websocket._originalIpc\n          ? false\n          : parsedUrl.host === websocket._originalHostOrSocketPath;\n\n      if (!isSameHost || (websocket._originalSecure && !isSecure)) {\n        //\n        // Match curl 7.77.0 behavior and drop the following headers. These\n        // headers are also dropped when following a redirect to a subdomain.\n        //\n        delete opts.headers.authorization;\n        delete opts.headers.cookie;\n\n        if (!isSameHost) delete opts.headers.host;\n\n        opts.auth = undefined;\n      }\n    }\n\n    //\n    // Match curl 7.77.0 behavior and make the first `Authorization` header win.\n    // If the `Authorization` header is set, then there is nothing to do as it\n    // will take precedence.\n    //\n    if (opts.auth && !options.headers.authorization) {\n      options.headers.authorization =\n        'Basic ' + Buffer.from(opts.auth).toString('base64');\n    }\n\n    req = websocket._req = request(opts);\n\n    if (websocket._redirects) {\n      //\n      // Unlike what is done for the `'upgrade'` event, no early exit is\n      // triggered here if the user calls `websocket.close()` or\n      // `websocket.terminate()` from a listener of the `'redirect'` event. This\n      // is because the user can also call `request.destroy()` with an error\n      // before calling `websocket.close()` or `websocket.terminate()` and this\n      // would result in an error being emitted on the `request` object with no\n      // `'error'` event listeners attached.\n      //\n      websocket.emit('redirect', websocket.url, req);\n    }\n  } else {\n    req = websocket._req = request(opts);\n  }\n\n  if (opts.timeout) {\n    req.on('timeout', () => {\n      abortHandshake(websocket, req, 'Opening handshake has timed out');\n    });\n  }\n\n  req.on('error', (err) => {\n    if (req === null || req[kAborted]) return;\n\n    req = websocket._req = null;\n    emitErrorAndClose(websocket, err);\n  });\n\n  req.on('response', (res) => {\n    const location = res.headers.location;\n    const statusCode = res.statusCode;\n\n    if (\n      location &&\n      opts.followRedirects &&\n      statusCode >= 300 &&\n      statusCode < 400\n    ) {\n      if (++websocket._redirects > opts.maxRedirects) {\n        abortHandshake(websocket, req, 'Maximum redirects exceeded');\n        return;\n      }\n\n      req.abort();\n\n      let addr;\n\n      try {\n        addr = new URL(location, address);\n      } catch (e) {\n        const err = new SyntaxError(`Invalid URL: ${location}`);\n        emitErrorAndClose(websocket, err);\n        return;\n      }\n\n      initAsClient(websocket, addr, protocols, options);\n    } else if (!websocket.emit('unexpected-response', req, res)) {\n      abortHandshake(\n        websocket,\n        req,\n        `Unexpected server response: ${res.statusCode}`\n      );\n    }\n  });\n\n  req.on('upgrade', (res, socket, head) => {\n    websocket.emit('upgrade', res);\n\n    //\n    // The user may have closed the connection from a listener of the\n    // `'upgrade'` event.\n    //\n    if (websocket.readyState !== WebSocket.CONNECTING) return;\n\n    req = websocket._req = null;\n\n    const upgrade = res.headers.upgrade;\n\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      abortHandshake(websocket, socket, 'Invalid Upgrade header');\n      return;\n    }\n\n    const digest = createHash('sha1')\n      .update(key + GUID)\n      .digest('base64');\n\n    if (res.headers['sec-websocket-accept'] !== digest) {\n      abortHandshake(websocket, socket, 'Invalid Sec-WebSocket-Accept header');\n      return;\n    }\n\n    const serverProt = res.headers['sec-websocket-protocol'];\n    let protError;\n\n    if (serverProt !== undefined) {\n      if (!protocolSet.size) {\n        protError = 'Server sent a subprotocol but none was requested';\n      } else if (!protocolSet.has(serverProt)) {\n        protError = 'Server sent an invalid subprotocol';\n      }\n    } else if (protocolSet.size) {\n      protError = 'Server sent no subprotocol';\n    }\n\n    if (protError) {\n      abortHandshake(websocket, socket, protError);\n      return;\n    }\n\n    if (serverProt) websocket._protocol = serverProt;\n\n    const secWebSocketExtensions = res.headers['sec-websocket-extensions'];\n\n    if (secWebSocketExtensions !== undefined) {\n      if (!perMessageDeflate) {\n        const message =\n          'Server sent a Sec-WebSocket-Extensions header but no extension ' +\n          'was requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      let extensions;\n\n      try {\n        extensions = parse(secWebSocketExtensions);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      const extensionNames = Object.keys(extensions);\n\n      if (\n        extensionNames.length !== 1 ||\n        extensionNames[0] !== PerMessageDeflate.extensionName\n      ) {\n        const message = 'Server indicated an extension that was not requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      try {\n        perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      websocket._extensions[PerMessageDeflate.extensionName] =\n        perMessageDeflate;\n    }\n\n    websocket.setSocket(socket, head, {\n      allowSynchronousEvents: opts.allowSynchronousEvents,\n      generateMask: opts.generateMask,\n      maxPayload: opts.maxPayload,\n      skipUTF8Validation: opts.skipUTF8Validation\n    });\n  });\n\n  if (opts.finishRequest) {\n    opts.finishRequest(req, websocket);\n  } else {\n    req.end();\n  }\n}\n\n/**\n * Emit the `'error'` and `'close'` events.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {Error} The error to emit\n * @private\n */\nfunction emitErrorAndClose(websocket, err) {\n  websocket._readyState = WebSocket.CLOSING;\n  //\n  // The following assignment is practically useless and is done only for\n  // consistency.\n  //\n  websocket._errorEmitted = true;\n  websocket.emit('error', err);\n  websocket.emitClose();\n}\n\n/**\n * Create a `net.Socket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {net.Socket} The newly created socket used to start the connection\n * @private\n */\nfunction netConnect(options) {\n  options.path = options.socketPath;\n  return net.connect(options);\n}\n\n/**\n * Create a `tls.TLSSocket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {tls.TLSSocket} The newly created socket used to start the connection\n * @private\n */\nfunction tlsConnect(options) {\n  options.path = undefined;\n\n  if (!options.servername && options.servername !== '') {\n    options.servername = net.isIP(options.host) ? '' : options.host;\n  }\n\n  return tls.connect(options);\n}\n\n/**\n * Abort the handshake and emit an error.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {(http.ClientRequest|net.Socket|tls.Socket)} stream The request to\n *     abort or the socket to destroy\n * @param {String} message The error message\n * @private\n */\nfunction abortHandshake(websocket, stream, message) {\n  websocket._readyState = WebSocket.CLOSING;\n\n  const err = new Error(message);\n  Error.captureStackTrace(err, abortHandshake);\n\n  if (stream.setHeader) {\n    stream[kAborted] = true;\n    stream.abort();\n\n    if (stream.socket && !stream.socket.destroyed) {\n      //\n      // On Node.js >= 14.3.0 `request.abort()` does not destroy the socket if\n      // called after the request completed. See\n      // https://github.com/websockets/ws/issues/1869.\n      //\n      stream.socket.destroy();\n    }\n\n    process.nextTick(emitErrorAndClose, websocket, err);\n  } else {\n    stream.destroy(err);\n    stream.once('error', websocket.emit.bind(websocket, 'error'));\n    stream.once('close', websocket.emitClose.bind(websocket));\n  }\n}\n\n/**\n * Handle cases where the `ping()`, `pong()`, or `send()` methods are called\n * when the `readyState` attribute is `CLOSING` or `CLOSED`.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {*} [data] The data to send\n * @param {Function} [cb] Callback\n * @private\n */\nfunction sendAfterClose(websocket, data, cb) {\n  if (data) {\n    const length = isBlob(data) ? data.size : toBuffer(data).length;\n\n    //\n    // The `_bufferedAmount` property is used only when the peer is a client and\n    // the opening handshake fails. Under these circumstances, in fact, the\n    // `setSocket()` method is not called, so the `_socket` and `_sender`\n    // properties are set to `null`.\n    //\n    if (websocket._socket) websocket._sender._bufferedBytes += length;\n    else websocket._bufferedAmount += length;\n  }\n\n  if (cb) {\n    const err = new Error(\n      `WebSocket is not open: readyState ${websocket.readyState} ` +\n        `(${readyStates[websocket.readyState]})`\n    );\n    process.nextTick(cb, err);\n  }\n}\n\n/**\n * The listener of the `Receiver` `'conclude'` event.\n *\n * @param {Number} code The status code\n * @param {Buffer} reason The reason for closing\n * @private\n */\nfunction receiverOnConclude(code, reason) {\n  const websocket = this[kWebSocket];\n\n  websocket._closeFrameReceived = true;\n  websocket._closeMessage = reason;\n  websocket._closeCode = code;\n\n  if (websocket._socket[kWebSocket] === undefined) return;\n\n  websocket._socket.removeListener('data', socketOnData);\n  process.nextTick(resume, websocket._socket);\n\n  if (code === 1005) websocket.close();\n  else websocket.close(code, reason);\n}\n\n/**\n * The listener of the `Receiver` `'drain'` event.\n *\n * @private\n */\nfunction receiverOnDrain() {\n  const websocket = this[kWebSocket];\n\n  if (!websocket.isPaused) websocket._socket.resume();\n}\n\n/**\n * The listener of the `Receiver` `'error'` event.\n *\n * @param {(RangeError|Error)} err The emitted error\n * @private\n */\nfunction receiverOnError(err) {\n  const websocket = this[kWebSocket];\n\n  if (websocket._socket[kWebSocket] !== undefined) {\n    websocket._socket.removeListener('data', socketOnData);\n\n    //\n    // On Node.js < 14.0.0 the `'error'` event is emitted synchronously. See\n    // https://github.com/websockets/ws/issues/1940.\n    //\n    process.nextTick(resume, websocket._socket);\n\n    websocket.close(err[kStatusCode]);\n  }\n\n  if (!websocket._errorEmitted) {\n    websocket._errorEmitted = true;\n    websocket.emit('error', err);\n  }\n}\n\n/**\n * The listener of the `Receiver` `'finish'` event.\n *\n * @private\n */\nfunction receiverOnFinish() {\n  this[kWebSocket].emitClose();\n}\n\n/**\n * The listener of the `Receiver` `'message'` event.\n *\n * @param {Buffer|ArrayBuffer|Buffer[])} data The message\n * @param {Boolean} isBinary Specifies whether the message is binary or not\n * @private\n */\nfunction receiverOnMessage(data, isBinary) {\n  this[kWebSocket].emit('message', data, isBinary);\n}\n\n/**\n * The listener of the `Receiver` `'ping'` event.\n *\n * @param {Buffer} data The data included in the ping frame\n * @private\n */\nfunction receiverOnPing(data) {\n  const websocket = this[kWebSocket];\n\n  if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);\n  websocket.emit('ping', data);\n}\n\n/**\n * The listener of the `Receiver` `'pong'` event.\n *\n * @param {Buffer} data The data included in the pong frame\n * @private\n */\nfunction receiverOnPong(data) {\n  this[kWebSocket].emit('pong', data);\n}\n\n/**\n * Resume a readable stream\n *\n * @param {Readable} stream The readable stream\n * @private\n */\nfunction resume(stream) {\n  stream.resume();\n}\n\n/**\n * The `Sender` error event handler.\n *\n * @param {Error} The error\n * @private\n */\nfunction senderOnError(err) {\n  const websocket = this[kWebSocket];\n\n  if (websocket.readyState === WebSocket.CLOSED) return;\n  if (websocket.readyState === WebSocket.OPEN) {\n    websocket._readyState = WebSocket.CLOSING;\n    setCloseTimer(websocket);\n  }\n\n  //\n  // `socket.end()` is used instead of `socket.destroy()` to allow the other\n  // peer to finish sending queued data. There is no need to set a timer here\n  // because `CLOSING` means that it is already set or not needed.\n  //\n  this._socket.end();\n\n  if (!websocket._errorEmitted) {\n    websocket._errorEmitted = true;\n    websocket.emit('error', err);\n  }\n}\n\n/**\n * Set a timer to destroy the underlying raw socket of a WebSocket.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @private\n */\nfunction setCloseTimer(websocket) {\n  websocket._closeTimer = setTimeout(\n    websocket._socket.destroy.bind(websocket._socket),\n    closeTimeout\n  );\n}\n\n/**\n * The listener of the socket `'close'` event.\n *\n * @private\n */\nfunction socketOnClose() {\n  const websocket = this[kWebSocket];\n\n  this.removeListener('close', socketOnClose);\n  this.removeListener('data', socketOnData);\n  this.removeListener('end', socketOnEnd);\n\n  websocket._readyState = WebSocket.CLOSING;\n\n  let chunk;\n\n  //\n  // The close frame might not have been received or the `'end'` event emitted,\n  // for example, if the socket was destroyed due to an error. Ensure that the\n  // `receiver` stream is closed after writing any remaining buffered data to\n  // it. If the readable side of the socket is in flowing mode then there is no\n  // buffered data as everything has been already written and `readable.read()`\n  // will return `null`. If instead, the socket is paused, any possible buffered\n  // data will be read as a single chunk.\n  //\n  if (\n    !this._readableState.endEmitted &&\n    !websocket._closeFrameReceived &&\n    !websocket._receiver._writableState.errorEmitted &&\n    (chunk = websocket._socket.read()) !== null\n  ) {\n    websocket._receiver.write(chunk);\n  }\n\n  websocket._receiver.end();\n\n  this[kWebSocket] = undefined;\n\n  clearTimeout(websocket._closeTimer);\n\n  if (\n    websocket._receiver._writableState.finished ||\n    websocket._receiver._writableState.errorEmitted\n  ) {\n    websocket.emitClose();\n  } else {\n    websocket._receiver.on('error', receiverOnFinish);\n    websocket._receiver.on('finish', receiverOnFinish);\n  }\n}\n\n/**\n * The listener of the socket `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction socketOnData(chunk) {\n  if (!this[kWebSocket]._receiver.write(chunk)) {\n    this.pause();\n  }\n}\n\n/**\n * The listener of the socket `'end'` event.\n *\n * @private\n */\nfunction socketOnEnd() {\n  const websocket = this[kWebSocket];\n\n  websocket._readyState = WebSocket.CLOSING;\n  websocket._receiver.end();\n  this.end();\n}\n\n/**\n * The listener of the socket `'error'` event.\n *\n * @private\n */\nfunction socketOnError() {\n  const websocket = this[kWebSocket];\n\n  this.removeListener('error', socketOnError);\n  this.on('error', NOOP);\n\n  if (websocket) {\n    websocket._readyState = WebSocket.CLOSING;\n    this.destroy();\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket.js\n");

/***/ })

};
;