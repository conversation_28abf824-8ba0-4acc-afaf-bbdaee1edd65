"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/members/page",{

/***/ "(app-pages-browser)/./src/app/members/page.tsx":
/*!**********************************!*\
  !*** ./src/app/members/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MembersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers_index__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/index */ \"(app-pages-browser)/./src/components/providers/index.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_export__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/export */ \"(app-pages-browser)/./src/lib/export.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/members/add-member-modal */ \"(app-pages-browser)/./src/components/members/add-member-modal.tsx\");\n/* harmony import */ var _components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/members/edit-member-modal */ \"(app-pages-browser)/./src/components/members/edit-member-modal.tsx\");\n/* harmony import */ var _components_members_view_member_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/members/view-member-modal */ \"(app-pages-browser)/./src/components/members/view-member-modal.tsx\");\n/* harmony import */ var _components_members_members_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/members/members-table */ \"(app-pages-browser)/./src/components/members/members-table.tsx\");\n/* harmony import */ var _components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/categories/categories-modal */ \"(app-pages-browser)/./src/components/categories/categories-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tags.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Remove database imports - using localStorage instead\n\n\n\n\n\n\n\n\nfunction MembersPage() {\n    _s();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMembers, setFilteredMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedMembers, setSelectedMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCategoriesModal, setShowCategoriesModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMember, setEditingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewingMember, setViewingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMembers();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterMembers();\n    }, [\n        members,\n        searchQuery,\n        statusFilter\n    ]);\n    const fetchMembers = ()=>{\n        try {\n            setLoading(true);\n            // Fetch users from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            // Fetch subscriptions from localStorage\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            // Map users with their subscriptions\n            const membersWithSubscriptions = users.map((user)=>({\n                    ...user,\n                    subscriptions: subscriptions.filter((sub)=>sub.user_id === user.id).map((sub)=>({\n                            id: sub.id,\n                            sport: sub.sport,\n                            plan_type: sub.plan_type,\n                            start_date: sub.start_date,\n                            end_date: sub.end_date,\n                            price_dzd: sub.price_dzd,\n                            status: sub.status\n                        }))\n                }));\n            setMembers(membersWithSubscriptions);\n        } catch (error) {\n            console.error(\"Error loading members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load members from localStorage\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMembers = ()=>{\n        // Add safety checks for member data\n        const validMembers = members.filter((member)=>member && typeof member === \"object\" && member.full_name && member.phone);\n        let filtered = validMembers.filter((member)=>{\n            var _member_full_name, _member_phone;\n            const nameMatch = ((_member_full_name = member.full_name) === null || _member_full_name === void 0 ? void 0 : _member_full_name.toLowerCase().includes(searchQuery.toLowerCase())) || false;\n            const phoneMatch = ((_member_phone = member.phone) === null || _member_phone === void 0 ? void 0 : _member_phone.includes(searchQuery)) || false;\n            const emailMatch = member.email ? member.email.toLowerCase().includes(searchQuery.toLowerCase()) : false;\n            return nameMatch || phoneMatch || emailMatch;\n        });\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((member)=>{\n                const subscriptions = member.subscriptions || [];\n                if (subscriptions.length === 0) {\n                    return statusFilter === \"expired\";\n                }\n                const activeSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(sub.end_date) === statusFilter);\n                return activeSubscriptions && activeSubscriptions.length > 0;\n            });\n        }\n        setFilteredMembers(filtered);\n    };\n    const getActiveSubscription = (member)=>{\n        if (!member) return null;\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return null;\n        // Get the most recent subscription (sorted by end date)\n        const sortedSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date).sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        return sortedSubscriptions[0] || null;\n    };\n    const getMemberStatus = (member)=>{\n        if (!member) return \"expired\";\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return \"expired\";\n        // Get the most recent subscription (active or most recent)\n        const sortedSubscriptions = subscriptions.sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        const latestSubscription = sortedSubscriptions[0];\n        if (!latestSubscription || !latestSubscription.end_date) return \"expired\";\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(latestSubscription.end_date);\n    };\n    const deleteMember = (memberId)=>{\n        if (!confirm(\"Are you sure you want to delete this member?\")) return;\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>user.id !== memberId);\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>sub.user_id !== memberId);\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: t(\"member_deleted\"),\n                description: \"Member has been successfully deleted\"\n            });\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting member:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete member\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Export functions\n    const handleExportCSV = ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        const csvData = (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.convertToCSV)(exportData);\n        (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadCSV)(csvData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to CSV successfully\"\n        });\n    };\n    const handleExportExcel = async ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        await (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadExcel)(exportData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to Excel successfully\"\n        });\n    };\n    // Bulk operations\n    const toggleSelectAll = ()=>{\n        if (selectedMembers.size === filteredMembers.length) {\n            setSelectedMembers(new Set());\n        } else {\n            setSelectedMembers(new Set(filteredMembers.map((m)=>m.id)));\n        }\n    };\n    const handleBulkDelete = ()=>{\n        if (selectedMembers.size === 0) return;\n        if (!confirm(\"Are you sure you want to delete \".concat(selectedMembers.size, \" members?\"))) return;\n        setBulkLoading(true);\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>!selectedMembers.has(user.id));\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>!selectedMembers.has(sub.user_id));\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: \"Bulk Delete Complete\",\n                description: \"\".concat(selectedMembers.size, \" members deleted successfully\")\n            });\n            setSelectedMembers(new Set());\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete selected members\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    const handleEditMember = (member)=>{\n        setEditingMember(member);\n        setShowEditModal(true);\n    };\n    const handleViewDetails = (member)=>{\n        setViewingMember(member);\n        setShowViewModal(true);\n    };\n    const handlePrintReport = (member)=>{\n        // Create a simple print report\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            const subscriptions = member.subscriptions || [];\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Member Report - \".concat(member.full_name, '</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; }\\n              .header { text-align: center; margin-bottom: 30px; }\\n              .info { margin-bottom: 20px; }\\n              .label { font-weight: bold; }\\n              table { width: 100%; border-collapse: collapse; margin-top: 20px; }\\n              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\\n              th { background-color: #f2f2f2; }\\n            </style>\\n          </head>\\n          <body>\\n            <div class=\"header\">\\n              <h1>\\xc9LITE CLUB</h1>\\n              <h2>Member Report</h2>\\n            </div>\\n            <div class=\"info\">\\n              <p><span class=\"label\">Name:</span> ').concat(member.full_name, '</p>\\n              <p><span class=\"label\">Phone:</span> ').concat(member.phone, '</p>\\n              <p><span class=\"label\">Email:</span> ').concat(member.email || \"N/A\", '</p>\\n              <p><span class=\"label\">Age:</span> ').concat(member.age, '</p>\\n              <p><span class=\"label\">Gender:</span> ').concat(member.gender, '</p>\\n              <p><span class=\"label\">Status:</span> ').concat(member.situation, '</p>\\n              <p><span class=\"label\">Member Since:</span> ').concat(new Date(member.created_at).toLocaleDateString(), \"</p>\\n            </div>\\n            <h3>Subscriptions</h3>\\n            <table>\\n              <tr>\\n                <th>Sport</th>\\n                <th>Plan</th>\\n                <th>Start Date</th>\\n                <th>End Date</th>\\n                <th>Price</th>\\n                <th>Status</th>\\n              </tr>\\n              \").concat(subscriptions.map((sub)=>\"\\n                <tr>\\n                  <td>\".concat(sub.sport, \"</td>\\n                  <td>\").concat(sub.plan_type, \"</td>\\n                  <td>\").concat(sub.start_date, \"</td>\\n                  <td>\").concat(sub.end_date, \"</td>\\n                  <td>\").concat(sub.price_dzd, \" DZD</td>\\n                  <td>\").concat(sub.status, \"</td>\\n                </tr>\\n              \")).join(\"\"), '\\n            </table>\\n            <div style=\"margin-top: 40px; text-align: center; font-size: 12px;\">\\n              <p>Generated on ').concat(new Date().toLocaleString(), \"</p>\\n              <p>All rights reserved - Powered by iCode DZ Tel: +213 551 93 05 89</p>\\n            </div>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"members\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                lineNumber: 389,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 388,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"members\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-black text-gray-900 dark:text-white tracking-tight\",\n                                    children: t(\"members_management\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-semibold text-gray-600 dark:text-gray-400\",\n                                    children: t(\"manage_gym_members\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                    onClick: handleExportCSV,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_csv\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                    onClick: handleExportExcel,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_excel\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                selectedMembers.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"bulk_operations\"),\n                                                    \" (\",\n                                                    selectedMembers.size,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                                                onClick: handleBulkDelete,\n                                                className: \"text-red-600 dark:text-red-400\",\n                                                disabled: bulkLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"delete_selected\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCategoriesModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Categories\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            onClick: ()=>setShowAddModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"add_new_member\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Total Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"active\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-600 dark:text-orange-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expiring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expiring\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expired\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: toggleSelectAll,\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            selectedMembers.size === filteredMembers.length && filteredMembers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: selectedMembers.size > 0 ? \"\".concat(selectedMembers.size, \" \").concat(t(\"selected_count\")) : t(\"select_all\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t(\"search_members\"),\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        \"all\",\n                                        \"active\",\n                                        \"expiring\",\n                                        \"expired\"\n                                    ].map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: statusFilter === status ? \"gym\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setStatusFilter(status),\n                                            className: \"capitalize\",\n                                            children: t(status)\n                                        }, status, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_members_table__WEBPACK_IMPORTED_MODULE_12__.MembersTable, {\n                            members: filteredMembers,\n                            loading: loading,\n                            selectedMembers: selectedMembers,\n                            onSelectionChange: setSelectedMembers,\n                            onEdit: handleEditMember,\n                            onDelete: deleteMember,\n                            onViewDetails: handleViewDetails,\n                            onPrintReport: handlePrintReport,\n                            onMemberUpdated: fetchMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 586,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 9\n                }, this),\n                filteredMembers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: t(\"no_members_found\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                children: searchQuery || statusFilter !== \"all\" ? t(\"try_adjusting_search\") : t(\"get_started_adding\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"gym\",\n                                onClick: ()=>setShowAddModal(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 17\n                                    }, this),\n                                    t(\"add_new_member\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_9__.AddMemberModal, {\n                    open: showAddModal,\n                    onOpenChange: setShowAddModal,\n                    onMemberAdded: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_10__.EditMemberModal, {\n                    open: showEditModal,\n                    onOpenChange: setShowEditModal,\n                    member: editingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 628,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_view_member_modal__WEBPACK_IMPORTED_MODULE_11__.ViewMemberModal, {\n                    open: showViewModal,\n                    onOpenChange: setShowViewModal,\n                    member: viewingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_13__.CategoriesModal, {\n                    open: showCategoriesModal,\n                    onOpenChange: setShowCategoriesModal,\n                    onSportsUpdated: ()=>{\n                        // Sports updated - could refresh sports list if needed\n                        toast({\n                            title: \"Sports Updated\",\n                            description: \"Sports have been updated successfully\"\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 642,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 398,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n        lineNumber: 397,\n        columnNumber: 5\n    }, this);\n}\n_s(MembersPage, \"m47C8TdiI4E5LPsyyxQ7vC+2co8=\", false, function() {\n    return [\n        _components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = MembersPage;\nvar _c;\n$RefreshReg$(_c, \"MembersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/members/page.tsx\n"));

/***/ })

});