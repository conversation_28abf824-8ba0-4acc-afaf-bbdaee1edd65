'use client'

import { useState } from 'react'
import { useLanguage } from '@/components/providers'
import { formatCurrency, formatDate, getDaysUntilExpiry, getSubscriptionStatus, calculateEndDate } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  User,
  Phone,
  Mail,
  Calendar,
  Activity,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  X,
} from 'lucide-react'

interface Member {
  id: string
  full_name: string
  gender: 'male' | 'female'
  age: number
  phone: string
  email?: string | null
  pregnant?: boolean
  situation: string
  remarks?: string | null
  created_at: string
  subscriptions?: Array<{
    id: string
    sport: string
    plan_type: 'monthly' | 'quarterly' | 'yearly'
    start_date: string
    end_date: string
    price_dzd: number
    status: 'active' | 'expiring' | 'expired'
  }>
}

interface ViewMemberModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: Member | null
  onMemberUpdated: () => void
}

export function ViewMemberModal({ open, onOpenChange, member, onMemberUpdated }: ViewMemberModalProps) {
  const [renewingSubscription, setRenewingSubscription] = useState<string | null>(null)
  const { t } = useLanguage()
  const { toast } = useToast()

  if (!member) return null

  const getActiveSubscription = () => {
    const subscriptions = member.subscriptions || []
    if (subscriptions.length === 0) return null
    return subscriptions.find(sub => sub.status === 'active') || subscriptions[0]
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="default" className="bg-green-500 hover:bg-green-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        )
      case 'expiring':
        return (
          <Badge variant="secondary" className="bg-yellow-500 hover:bg-yellow-600 text-white">
            <Clock className="w-3 h-3 mr-1" />
            Expiring
          </Badge>
        )
      case 'expired':
        return (
          <Badge variant="destructive">
            <X className="w-3 h-3 mr-1" />
            Expired
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const handleRenewSubscription = (subscription: any) => {
    setRenewingSubscription(subscription.id)

    try {
      // Calculate new dates
      const startDate = new Date().toISOString().split('T')[0]
      const endDate = calculateEndDate(startDate, subscription.plan_type)

      // Create new subscription with same details
      const renewalData = {
        id: Date.now().toString(),
        user_id: member.id,
        sport: subscription.sport,
        plan_type: subscription.plan_type,
        start_date: startDate,
        end_date: endDate,
        price_dzd: subscription.price_dzd,
        status: 'active' as 'active' | 'expiring' | 'expired',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      // Save to localStorage
      const storedSubscriptions = localStorage.getItem('gym_subscriptions')
      const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : []
      subscriptions.push(renewalData)
      localStorage.setItem('gym_subscriptions', JSON.stringify(subscriptions))

      toast({
        title: 'Subscription Renewed',
        description: `Subscription renewed for ${member.full_name} until ${endDate}`,
      })

      onMemberUpdated()
    } catch (error: any) {
      console.error('Error renewing subscription:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to renew subscription',
        variant: 'destructive',
      })
    } finally {
      setRenewingSubscription(null)
    }
  }

  const activeSubscription = getActiveSubscription()
  const daysUntilExpiry = activeSubscription ? getDaysUntilExpiry(activeSubscription.end_date) : null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>Member Details - {member.full_name}</span>
          </DialogTitle>
          <DialogDescription>
            Complete member information and subscription details
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Personal Information */}
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="w-4 h-4" />
                <span>Personal Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {member.full_name.charAt(0).toUpperCase()}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    {member.full_name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {member.gender === 'male' ? '♂ Male' : '♀ Female'} • {member.age} years old
                    {member.pregnant && ' • Pregnant'}
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white">{member.phone}</span>
                </div>
                
                {member.email && (
                  <div className="flex items-center space-x-3">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-900 dark:text-white">{member.email}</span>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white">
                    Member since {formatDate(member.created_at)}
                  </span>
                </div>

                <div className="flex items-center space-x-3">
                  <Activity className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white capitalize">
                    Status: {member.situation}
                  </span>
                </div>
              </div>

              {member.remarks && (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <strong>Remarks:</strong> {member.remarks}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Subscription Information */}
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-4 h-4" />
                <span>Subscription Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {activeSubscription ? (
                <>
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-900 dark:text-white">
                      {activeSubscription.sport}
                    </span>
                    {getStatusBadge(activeSubscription.status)}
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Plan Type</p>
                      <p className="font-medium text-gray-900 dark:text-white capitalize">
                        {activeSubscription.plan_type}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Price</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {formatCurrency(activeSubscription.price_dzd)}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Start Date</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {formatDate(activeSubscription.start_date)}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">End Date</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {formatDate(activeSubscription.end_date)}
                      </p>
                    </div>
                  </div>

                  {daysUntilExpiry !== null && (
                    <div className={`rounded-lg p-3 ${
                      daysUntilExpiry < 0 ? 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800' :
                      daysUntilExpiry <= 7 ? 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800' :
                      'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                    }`}>
                      <div className="flex items-center space-x-2">
                        {daysUntilExpiry < 0 ? (
                          <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400" />
                        ) : daysUntilExpiry <= 7 ? (
                          <Clock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                        ) : (
                          <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                        )}
                        <p className={`text-sm font-medium ${
                          daysUntilExpiry < 0 ? 'text-red-600 dark:text-red-400' :
                          daysUntilExpiry <= 7 ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>
                          {daysUntilExpiry < 0 ? `Expired ${Math.abs(daysUntilExpiry)} days ago` :
                           daysUntilExpiry === 0 ? 'Expires today' :
                           `${daysUntilExpiry} days remaining`}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Renewal Button */}
                  <Button
                    onClick={() => handleRenewSubscription(activeSubscription)}
                    disabled={renewingSubscription === activeSubscription.id}
                    className="w-full"
                    variant="gym"
                  >
                    {renewingSubscription === activeSubscription.id ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    ) : (
                      <RefreshCw className="w-4 h-4 mr-2" />
                    )}
                    Renew Subscription
                  </Button>
                </>
              ) : (
                <div className="text-center py-8">
                  <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No Active Subscription
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    This member doesn't have an active subscription
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* All Subscriptions History */}
        {member.subscriptions && member.subscriptions.length > 1 && (
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>Subscription History</CardTitle>
              <CardDescription>
                All past and current subscriptions for this member
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {member.subscriptions.map((subscription) => (
                  <div key={subscription.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {subscription.sport}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {formatDate(subscription.start_date)} - {formatDate(subscription.end_date)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(subscription.price_dzd)}
                      </span>
                      {getStatusBadge(subscription.status)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Close Button */}
        <div className="flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
