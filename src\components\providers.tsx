'use client'

import { createContext, useContext, useEffect, useState } from 'react'

// Theme Context
interface ThemeContextType {
  theme: 'light' | 'dark'
  toggleTheme: () => void
}

const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  toggleTheme: () => {},
})

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Language Context
interface LanguageContextType {
  language: 'en' | 'fr' | 'ar'
  setLanguage: (lang: 'en' | 'fr' | 'ar') => void
  t: (key: string) => string
}

const LanguageContext = createContext<LanguageContextType>({
  language: 'en',
  setLanguage: () => {},
  t: (key: string) => key,
})

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

// Simple translations
const translations = {
  en: {
    'members': 'Members',
    'dashboard': 'Dashboard',
    'pos': 'Point of Sale',
    'inventory': 'Inventory',
    'sports': 'Sports',
    'classes': 'Classes',
    'reports': 'Reports',
    'settings': 'Settings',
    'logout': 'Logout',
    'add_member': 'Add Member',
    'edit_member': 'Edit Member',
    'delete_member': 'Delete Member',
    'save': 'Save',
    'cancel': 'Cancel',
    'name': 'Name',
    'email': 'Email',
    'phone': 'Phone',
    'category': 'Category',
    'manage_sports': 'Manage Sports',
    'manage_sports_description': 'Add, edit, or delete sports categories for your gym management system',
    'edit_sport': 'Edit Sport',
    'member_updated': 'Member Updated',
    'add_new_sport': 'Add New Sport',
    'gender': 'Gender',
    'male': 'Male',
    'female': 'Female',
    'sport': 'Sport',
  },
  fr: {
    'members': 'Membres',
    'dashboard': 'Tableau de bord',
    'pos': 'Point de vente',
    'inventory': 'Inventaire',
    'sports': 'Sports',
    'classes': 'Cours',
    'reports': 'Rapports',
    'settings': 'Paramètres',
    'logout': 'Déconnexion',
    'add_member': 'Ajouter un membre',
    'edit_member': 'Modifier le membre',
    'delete_member': 'Supprimer le membre',
    'save': 'Enregistrer',
    'cancel': 'Annuler',
    'name': 'Nom',
    'email': 'Email',
    'phone': 'Téléphone',
    'category': 'Catégorie',
    'manage_sports': 'Gérer les Sports',
    'manage_sports_description': 'Ajouter, modifier ou supprimer les catégories de sports pour votre système de gestion de salle de sport',
    'edit_sport': 'Modifier le Sport',
    'member_updated': 'Membre Mis à Jour',
    'add_new_sport': 'Ajouter un Nouveau Sport',
    'gender': 'Genre',
    'male': 'Homme',
    'female': 'Femme',
    'sport': 'Sport',
  },
  ar: {
    'members': 'الأعضاء',
    'dashboard': 'لوحة التحكم',
    'pos': 'نقطة البيع',
    'inventory': 'المخزون',
    'sports': 'الرياضات',
    'classes': 'الفصول',
    'reports': 'التقارير',
    'settings': 'الإعدادات',
    'logout': 'تسجيل الخروج',
    'add_member': 'إضافة عضو',
    'edit_member': 'تعديل العضو',
    'delete_member': 'حذف العضو',
    'save': 'حفظ',
    'cancel': 'إلغاء',
    'name': 'الاسم',
    'email': 'البريد الإلكتروني',
    'phone': 'الهاتف',
    'category': 'الفئة',
    'manage_sports': 'إدارة الرياضات',
    'manage_sports_description': 'إضافة وتعديل وحذف فئات الرياضة لنظام إدارة النادي',
    'edit_sport': 'تعديل الرياضة',
    'member_updated': 'تم تحديث العضو',
    'add_new_sport': 'إضافة رياضة جديدة',
    'gender': 'الجنس',
    'male': 'ذكر',
    'female': 'أنثى',
    'sport': 'الرياضة',
  },
}

export function Providers({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<'light' | 'dark'>('light')
  const [language, setLanguage] = useState<'en' | 'fr' | 'ar'>('en')

  useEffect(() => {
    // Load theme from localStorage
    const savedTheme = localStorage.getItem('gym-theme') as 'light' | 'dark'
    if (savedTheme) {
      setTheme(savedTheme)
      document.documentElement.classList.toggle('dark', savedTheme === 'dark')
    }

    // Load language from localStorage
    const savedLanguage = localStorage.getItem('gym-language') as 'en' | 'fr' | 'ar'
    if (savedLanguage) {
      setLanguage(savedLanguage)
      document.documentElement.setAttribute('lang', savedLanguage)
      document.documentElement.setAttribute('dir', savedLanguage === 'ar' ? 'rtl' : 'ltr')
    }
  }, [])

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
    localStorage.setItem('gym-theme', newTheme)
    document.documentElement.classList.toggle('dark', newTheme === 'dark')
  }

  const handleSetLanguage = (lang: 'en' | 'fr' | 'ar') => {
    setLanguage(lang)
    localStorage.setItem('gym-language', lang)
    document.documentElement.setAttribute('lang', lang)
    document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr')
  }

  const t = (key: string): string => {
    return (translations[language] as any)?.[key] || key
  }

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
        {children}
      </LanguageContext.Provider>
    </ThemeContext.Provider>
  )
}
