'use client'

import { useTheme, useLanguage } from '@/components/providers'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { Footer } from './footer'
import { cn } from '@/lib/utils'

interface MainLayoutProps {
  children: React.ReactNode
  title?: string
  className?: string
}

export function MainLayout({ children, title, className }: MainLayoutProps) {
  // No authentication required - direct access

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      <Sidebar />
      <div className="flex flex-col min-h-screen flex-1 overflow-hidden">
        <Header title={title} />
        <main
          className={cn(
            'flex-1 p-4 md:p-6 overflow-auto',
            className
          )}
        >
          {children}
        </main>
        <Footer />
      </div>
    </div>
  )
}
