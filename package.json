{"name": "pos-gym-elite", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/supabase-js": "^2.38.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.292.0", "next": "^14.0.0", "next-i18next": "^15.0.0", "next-pwa": "^5.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-i18next": "^13.5.0", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.11", "autoprefixer": "^10.4.16", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}, "keywords": ["gym", "management", "pos", "supabase", "nextjs"], "author": "Gym Elite", "license": "MIT", "description": "Complete Gym Management System with POS, Member Management, and Analytics"}