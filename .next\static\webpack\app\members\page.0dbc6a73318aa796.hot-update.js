"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/members/page",{

/***/ "(app-pages-browser)/./src/app/members/page.tsx":
/*!**********************************!*\
  !*** ./src/app/members/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MembersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers_index__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/index */ \"(app-pages-browser)/./src/components/providers/index.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_export__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/export */ \"(app-pages-browser)/./src/lib/export.ts\");\n/* harmony import */ var _lib_test_data__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/test-data */ \"(app-pages-browser)/./src/lib/test-data.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/members/add-member-modal */ \"(app-pages-browser)/./src/components/members/add-member-modal.tsx\");\n/* harmony import */ var _components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/members/edit-member-modal */ \"(app-pages-browser)/./src/components/members/edit-member-modal.tsx\");\n/* harmony import */ var _components_members_view_member_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/members/view-member-modal */ \"(app-pages-browser)/./src/components/members/view-member-modal.tsx\");\n/* harmony import */ var _components_members_members_table__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/members/members-table */ \"(app-pages-browser)/./src/components/members/members-table.tsx\");\n/* harmony import */ var _components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/categories/categories-modal */ \"(app-pages-browser)/./src/components/categories/categories-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tags.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Remove database imports - using localStorage instead\n\n\n\n\n\n\n\n\nfunction MembersPage() {\n    _s();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMembers, setFilteredMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedMembers, setSelectedMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCategoriesModal, setShowCategoriesModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMember, setEditingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewingMember, setViewingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMembers();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterMembers();\n    }, [\n        members,\n        searchQuery,\n        statusFilter\n    ]);\n    const fetchMembers = ()=>{\n        try {\n            setLoading(true);\n            // Fetch users from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            // Fetch subscriptions from localStorage\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            // Map users with their subscriptions\n            const membersWithSubscriptions = users.map((user)=>({\n                    ...user,\n                    subscriptions: subscriptions.filter((sub)=>sub.user_id === user.id).map((sub)=>({\n                            id: sub.id,\n                            sport: sub.sport,\n                            plan_type: sub.plan_type,\n                            start_date: sub.start_date,\n                            end_date: sub.end_date,\n                            price_dzd: sub.price_dzd,\n                            status: sub.status\n                        }))\n                }));\n            setMembers(membersWithSubscriptions);\n        } catch (error) {\n            console.error(\"Error loading members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load members from localStorage\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMembers = ()=>{\n        // Add safety checks for member data\n        const validMembers = members.filter((member)=>member && typeof member === \"object\" && member.full_name && member.phone);\n        let filtered = validMembers.filter((member)=>{\n            var _member_full_name, _member_phone;\n            const nameMatch = ((_member_full_name = member.full_name) === null || _member_full_name === void 0 ? void 0 : _member_full_name.toLowerCase().includes(searchQuery.toLowerCase())) || false;\n            const phoneMatch = ((_member_phone = member.phone) === null || _member_phone === void 0 ? void 0 : _member_phone.includes(searchQuery)) || false;\n            const emailMatch = member.email ? member.email.toLowerCase().includes(searchQuery.toLowerCase()) : false;\n            return nameMatch || phoneMatch || emailMatch;\n        });\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((member)=>{\n                const subscriptions = member.subscriptions || [];\n                if (subscriptions.length === 0) {\n                    return statusFilter === \"expired\";\n                }\n                const activeSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(sub.end_date) === statusFilter);\n                return activeSubscriptions && activeSubscriptions.length > 0;\n            });\n        }\n        setFilteredMembers(filtered);\n    };\n    const getActiveSubscription = (member)=>{\n        if (!member) return null;\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return null;\n        // Get the most recent subscription (sorted by end date)\n        const sortedSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date).sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        return sortedSubscriptions[0] || null;\n    };\n    const getMemberStatus = (member)=>{\n        if (!member) return \"expired\";\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return \"expired\";\n        // Get the most recent subscription (active or most recent)\n        const sortedSubscriptions = subscriptions.sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        const latestSubscription = sortedSubscriptions[0];\n        if (!latestSubscription || !latestSubscription.end_date) return \"expired\";\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(latestSubscription.end_date);\n    };\n    const deleteMember = (memberId)=>{\n        if (!confirm(\"Are you sure you want to delete this member?\")) return;\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>user.id !== memberId);\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>sub.user_id !== memberId);\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: t(\"member_deleted\"),\n                description: \"Member has been successfully deleted\"\n            });\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting member:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete member\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Export functions\n    const handleExportCSV = ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        const csvData = (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.convertToCSV)(exportData);\n        (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadCSV)(csvData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to CSV successfully\"\n        });\n    };\n    const handleExportExcel = async ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        await (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadExcel)(exportData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to Excel successfully\"\n        });\n    };\n    // Bulk operations\n    const toggleSelectAll = ()=>{\n        if (selectedMembers.size === filteredMembers.length) {\n            setSelectedMembers(new Set());\n        } else {\n            setSelectedMembers(new Set(filteredMembers.map((m)=>m.id)));\n        }\n    };\n    const handleBulkDelete = ()=>{\n        if (selectedMembers.size === 0) return;\n        if (!confirm(\"Are you sure you want to delete \".concat(selectedMembers.size, \" members?\"))) return;\n        setBulkLoading(true);\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>!selectedMembers.has(user.id));\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>!selectedMembers.has(sub.user_id));\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: \"Bulk Delete Complete\",\n                description: \"\".concat(selectedMembers.size, \" members deleted successfully\")\n            });\n            setSelectedMembers(new Set());\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete selected members\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    const handleEditMember = (member)=>{\n        setEditingMember(member);\n        setShowEditModal(true);\n    };\n    const handleCreateTestData = ()=>{\n        (0,_lib_test_data__WEBPACK_IMPORTED_MODULE_8__.createTestMembers)();\n        toast({\n            title: \"Test Data Created\",\n            description: \"Test members with different subscription statuses have been created\"\n        });\n        fetchMembers();\n    };\n    const handleClearTestData = ()=>{\n        if (confirm(\"Are you sure you want to clear all data? This cannot be undone.\")) {\n            (0,_lib_test_data__WEBPACK_IMPORTED_MODULE_8__.clearTestData)();\n            toast({\n                title: \"Data Cleared\",\n                description: \"All test data has been removed\"\n            });\n            fetchMembers();\n        }\n    };\n    const handleViewDetails = (member)=>{\n        setViewingMember(member);\n        setShowViewModal(true);\n    };\n    const handlePrintReport = (member)=>{\n        // Create a simple print report\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            const subscriptions = member.subscriptions || [];\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Member Report - \".concat(member.full_name, '</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; }\\n              .header { text-align: center; margin-bottom: 30px; }\\n              .info { margin-bottom: 20px; }\\n              .label { font-weight: bold; }\\n              table { width: 100%; border-collapse: collapse; margin-top: 20px; }\\n              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\\n              th { background-color: #f2f2f2; }\\n            </style>\\n          </head>\\n          <body>\\n            <div class=\"header\">\\n              <h1>\\xc9LITE CLUB</h1>\\n              <h2>Member Report</h2>\\n            </div>\\n            <div class=\"info\">\\n              <p><span class=\"label\">Name:</span> ').concat(member.full_name, '</p>\\n              <p><span class=\"label\">Phone:</span> ').concat(member.phone, '</p>\\n              <p><span class=\"label\">Email:</span> ').concat(member.email || \"N/A\", '</p>\\n              <p><span class=\"label\">Age:</span> ').concat(member.age, '</p>\\n              <p><span class=\"label\">Gender:</span> ').concat(member.gender, '</p>\\n              <p><span class=\"label\">Status:</span> ').concat(member.situation, '</p>\\n              <p><span class=\"label\">Member Since:</span> ').concat(new Date(member.created_at).toLocaleDateString(), \"</p>\\n            </div>\\n            <h3>Subscriptions</h3>\\n            <table>\\n              <tr>\\n                <th>Sport</th>\\n                <th>Plan</th>\\n                <th>Start Date</th>\\n                <th>End Date</th>\\n                <th>Price</th>\\n                <th>Status</th>\\n              </tr>\\n              \").concat(subscriptions.map((sub)=>\"\\n                <tr>\\n                  <td>\".concat(sub.sport, \"</td>\\n                  <td>\").concat(sub.plan_type, \"</td>\\n                  <td>\").concat(sub.start_date, \"</td>\\n                  <td>\").concat(sub.end_date, \"</td>\\n                  <td>\").concat(sub.price_dzd, \" DZD</td>\\n                  <td>\").concat(sub.status, \"</td>\\n                </tr>\\n              \")).join(\"\"), '\\n            </table>\\n            <div style=\"margin-top: 40px; text-align: center; font-size: 12px;\">\\n              <p>Generated on ').concat(new Date().toLocaleString(), \"</p>\\n              <p>All rights reserved - Powered by iCode DZ Tel: +213 551 93 05 89</p>\\n            </div>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"members\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                lineNumber: 409,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 408,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"members\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-black text-gray-900 dark:text-white tracking-tight\",\n                                    children: t(\"members_management\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-semibold text-gray-600 dark:text-gray-400\",\n                                    children: t(\"manage_gym_members\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuItem, {\n                                                    onClick: handleExportCSV,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_csv\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuItem, {\n                                                    onClick: handleExportExcel,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_excel\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                selectedMembers.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"bulk_operations\"),\n                                                    \" (\",\n                                                    selectedMembers.size,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_15__.DropdownMenuItem, {\n                                                onClick: handleBulkDelete,\n                                                className: \"text-red-600 dark:text-red-400\",\n                                                disabled: bulkLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"delete_selected\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCategoriesModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Categories\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                         true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: handleCreateTestData,\n                                                    className: \"text-blue-600\",\n                                                    children: \"Create Test Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: handleClearTestData,\n                                                    className: \"text-red-600\",\n                                                    children: \"Clear Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            onClick: ()=>setShowAddModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"add_new_member\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Total Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"active\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-600 dark:text-orange-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expiring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expiring\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expired\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: toggleSelectAll,\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            selectedMembers.size === filteredMembers.length && filteredMembers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: selectedMembers.size > 0 ? \"\".concat(selectedMembers.size, \" \").concat(t(\"selected_count\")) : t(\"select_all\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t(\"search_members\"),\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        \"all\",\n                                        \"active\",\n                                        \"expiring\",\n                                        \"expired\"\n                                    ].map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: statusFilter === status ? \"gym\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setStatusFilter(status),\n                                            className: \"capitalize\",\n                                            children: t(status)\n                                        }, status, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_members_table__WEBPACK_IMPORTED_MODULE_13__.MembersTable, {\n                            members: filteredMembers,\n                            loading: loading,\n                            selectedMembers: selectedMembers,\n                            onSelectionChange: setSelectedMembers,\n                            onEdit: handleEditMember,\n                            onDelete: deleteMember,\n                            onViewDetails: handleViewDetails,\n                            onPrintReport: handlePrintReport,\n                            onMemberUpdated: fetchMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, this),\n                filteredMembers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: t(\"no_members_found\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                children: searchQuery || statusFilter !== \"all\" ? t(\"try_adjusting_search\") : t(\"get_started_adding\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"gym\",\n                                onClick: ()=>setShowAddModal(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 17\n                                    }, this),\n                                    t(\"add_new_member\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_10__.AddMemberModal, {\n                    open: showAddModal,\n                    onOpenChange: setShowAddModal,\n                    onMemberAdded: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 655,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_11__.EditMemberModal, {\n                    open: showEditModal,\n                    onOpenChange: setShowEditModal,\n                    member: editingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 661,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_view_member_modal__WEBPACK_IMPORTED_MODULE_12__.ViewMemberModal, {\n                    open: showViewModal,\n                    onOpenChange: setShowViewModal,\n                    member: viewingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_14__.CategoriesModal, {\n                    open: showCategoriesModal,\n                    onOpenChange: setShowCategoriesModal,\n                    onSportsUpdated: ()=>{\n                        // Sports updated - could refresh sports list if needed\n                        toast({\n                            title: \"Sports Updated\",\n                            description: \"Sports have been updated successfully\"\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 418,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n        lineNumber: 417,\n        columnNumber: 5\n    }, this);\n}\n_s(MembersPage, \"m47C8TdiI4E5LPsyyxQ7vC+2co8=\", false, function() {\n    return [\n        _components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = MembersPage;\nvar _c;\n$RefreshReg$(_c, \"MembersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/members/page.tsx\n"));

/***/ })

});